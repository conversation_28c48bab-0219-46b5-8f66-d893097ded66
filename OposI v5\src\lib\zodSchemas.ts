// Directorio para esquemas Zod reutilizables
import { z } from 'zod';

export const DocumentoSchema = z.object({
  titulo: z.string().min(1).max(200),
  contenido: z.string().min(1),
  categoria: z.string().optional(),
  numero_tema: z.number().int().positive().optional(),
});

export const PreguntaSchema = z.object({
  pregunta: z.string().min(1).max(500),
  documentos: z.array(DocumentoSchema).min(1),
});

export const GenerarTestSchema = z.object({
  action: z.literal('generarTest'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
});

export const GenerarFlashcardsSchema = z.object({
  action: z.literal('generarFlashcards'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
});

export const GenerarMapaMentalSchema = z.object({
  action: z.literal('generarMapaMental'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
});

export const ApiGeminiInputSchema = z.union([
  PreguntaSchema,
  GenerarTestSchema,
  GenerarFlashcardsSchema,
  GenerarMapaMentalSchema,
]);
