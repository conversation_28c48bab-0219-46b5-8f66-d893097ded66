// Script de prueba para verificar que los formularios funcionen correctamente
const { z } = require('zod');

// Importar los esquemas (simulados para la prueba)
const generatorFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
});

const testFormSchema = generatorFormSchema;
const flashcardFormSchema = generatorFormSchema;
const mindMapFormSchema = generatorFormSchema;

// Datos de prueba
const testData = {
  peticion: 'Genera un test sobre los conceptos principales del tema 1'
};

const invalidData = {
  peticion: ''
};

console.log('🧪 Probando esquemas de validación...\n');

// Probar testFormSchema
console.log('📝 Probando testFormSchema:');
try {
  const result = testFormSchema.parse(testData);
  console.log('✅ Datos válidos:', result);
} catch (error) {
  console.log('❌ Error:', error.errors);
}

try {
  const result = testFormSchema.parse(invalidData);
  console.log('✅ Datos válidos:', result);
} catch (error) {
  console.log('✅ Error esperado para datos inválidos:', error.errors[0].message);
}

// Probar flashcardFormSchema
console.log('\n🃏 Probando flashcardFormSchema:');
try {
  const result = flashcardFormSchema.parse(testData);
  console.log('✅ Datos válidos:', result);
} catch (error) {
  console.log('❌ Error:', error.errors);
}

// Probar mindMapFormSchema
console.log('\n🧠 Probando mindMapFormSchema:');
try {
  const result = mindMapFormSchema.parse(testData);
  console.log('✅ Datos válidos:', result);
} catch (error) {
  console.log('❌ Error:', error.errors);
}

console.log('\n✨ Todas las pruebas completadas!');
