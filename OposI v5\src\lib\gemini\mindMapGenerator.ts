import { model, prepararDocumentos } from './geminiClient';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Construir un prompt simple y robusto
    const prompt = `
Eres "Mentor Opositor AI". Crea un mapa mental SIMPLE y FUNCIONAL basado en el contenido proporcionado.

CONTENIDO:
${contenidoDocumentos}

INSTRUCCIONES DEL USUARIO:
${instrucciones || 'Crea un mapa mental que organice los conceptos principales del contenido.'}

GENERA UN ARCHIVO HTML COMPLETO que contenga:

1. DOCTYPE html y estructura HTML básica
2. D3.js desde CDN: https://d3js.org/d3.v7.min.js
3. CSS simple en <style>
4. JavaScript con D3.js en <script>

CÓDIGO D3.JS REQUERIDO (usa exactamente este patrón):

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mapa Mental</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f5f5f5; }
        .link { fill: none; stroke: #ccc; stroke-width: 2px; }
        .node circle { fill: #fff; stroke: steelblue; stroke-width: 2px; }
        .node text { font: 12px sans-serif; }
    </style>
</head>
<body>
    <script>
        // Datos del mapa mental (ADAPTA SEGÚN EL CONTENIDO)
        const data = {
            name: "Tema Principal",
            children: [
                { name: "Subtema 1" },
                { name: "Subtema 2" },
                { name: "Subtema 3" }
            ]
        };

        // Configuración
        const width = 800;
        const height = 600;
        const margin = { top: 20, right: 90, bottom: 30, left: 90 };

        // Crear SVG
        const svg = d3.select("body").append("svg")
            .attr("width", width)
            .attr("height", height);

        const g = svg.append("g")
            .attr("transform", "translate(" + margin.left + "," + margin.top + ")");

        // Layout de árbol
        const tree = d3.tree()
            .size([height - margin.top - margin.bottom, width - margin.left - margin.right]);

        // Procesar datos
        const root = d3.hierarchy(data);
        tree(root);

        // Enlaces
        g.selectAll(".link")
            .data(root.links())
            .enter().append("path")
            .attr("class", "link")
            .attr("d", d3.linkHorizontal()
                .x(d => d.y)
                .y(d => d.x));

        // Nodos
        const node = g.selectAll(".node")
            .data(root.descendants())
            .enter().append("g")
            .attr("class", "node")
            .attr("transform", d => "translate(" + d.y + "," + d.x + ")");

        // Círculos
        node.append("circle")
            .attr("r", 5);

        // Texto
        node.append("text")
            .attr("dy", ".35em")
            .attr("x", d => d.children ? -13 : 13)
            .style("text-anchor", d => d.children ? "end" : "start")
            .text(d => d.data.name);
    </script>
</body>
</html>

IMPORTANTE:
- Adapta SOLO la estructura de datos (variable 'data') según el contenido proporcionado
- NO modifiques el código D3.js
- Responde ÚNICAMENTE con el HTML completo
- NO incluyas explicaciones fuera del código
`;

    // Generar el mapa mental
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)
    // El mapa mental se genera como HTML con D3.js, no como JSON

    // Verificar que la respuesta contiene HTML válido
    if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {
      console.error('Respuesta de Gemini para mapa mental:', response);
      throw new Error("La respuesta no contiene HTML válido para el mapa mental.");
    }

    // Retornar el HTML completo como string
    return response;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}
