"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FlashcardGenerator.tsx":
/*!***********************************************!*\
  !*** ./src/components/FlashcardGenerator.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FlashcardGenerator(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [tituloColeccion, setTituloColeccion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [descripcionColeccion, setDescripcionColeccion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [flashcardsGeneradas, setFlashcardsGeneradas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionGuardada, setColeccionGuardada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [coleccionesExistentes, setColeccionesExistentes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('nueva');\n    const [cargandoColecciones, setCargandoColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_4__.flashcardFormSchema),\n        defaultValues: {\n            peticion: ''\n        }\n    });\n    // Cargar colecciones existentes al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardGenerator.useEffect\": ()=>{\n            cargarColecciones();\n        }\n    }[\"FlashcardGenerator.useEffect\"], []);\n    const cargarColecciones = async ()=>{\n        setCargandoColecciones(true);\n        try {\n            const colecciones = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n            setColeccionesExistentes(colecciones);\n        } catch (error) {\n            console.error('Error al cargar colecciones:', error);\n            setError('No se pudieron cargar las colecciones existentes.');\n        } finally{\n            setCargandoColecciones(false);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        setError('');\n        setFlashcardsGeneradas([]);\n        setColeccionGuardada(false);\n        try {\n            const contextos = documentosSeleccionados.map((doc)=>doc.contenido);\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'generarFlashcards',\n                    peticion: data.peticion,\n                    contextos\n                })\n            });\n            if (!response.ok) throw new Error('Error en la API interna');\n            const { result: flashcards } = await response.json();\n            setFlashcardsGeneradas(flashcards);\n            if (!tituloColeccion) {\n                setTituloColeccion(\"Flashcards: \".concat(data.peticion.substring(0, 50)).concat(data.peticion.length > 50 ? '...' : ''));\n            }\n        } catch (error) {\n            setError('Ha ocurrido un error al generar las flashcards. Por favor, inténtalo de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGuardarColeccion = async ()=>{\n        if (flashcardsGeneradas.length === 0) {\n            setError('No hay flashcards para guardar');\n            return;\n        }\n        // Si se seleccionó crear una nueva colección, validar el título\n        if (coleccionSeleccionada === 'nueva' && !tituloColeccion.trim()) {\n            setError('Por favor, proporciona un título para la nueva colección');\n            return;\n        }\n        // Si se seleccionó una colección existente, validar que se haya seleccionado una\n        if (coleccionSeleccionada !== 'nueva' && coleccionSeleccionada === '') {\n            setError('Por favor, selecciona una colección existente');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        try {\n            let coleccionId;\n            // Si es una nueva colección, crearla\n            if (coleccionSeleccionada === 'nueva') {\n                coleccionId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearColeccionFlashcards)(tituloColeccion, descripcionColeccion);\n                if (!coleccionId) {\n                    throw new Error('No se pudo crear la colección');\n                }\n            } else {\n                // Usar la colección existente seleccionada\n                coleccionId = coleccionSeleccionada;\n            }\n            // Preparar las flashcards para guardar\n            const flashcardsParaGuardar = flashcardsGeneradas.map((fc)=>({\n                    coleccion_id: coleccionId,\n                    pregunta: fc.pregunta,\n                    respuesta: fc.respuesta\n                }));\n            // Guardar las flashcards\n            const resultado = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarFlashcards)(flashcardsParaGuardar);\n            if (!resultado) {\n                throw new Error('No se pudieron guardar las flashcards');\n            }\n            setColeccionGuardada(true);\n            // Recargar las colecciones para tener la lista actualizada\n            if (coleccionSeleccionada === 'nueva') {\n                await cargarColecciones();\n            }\n        } catch (error) {\n            console.error('Error al guardar las flashcards:', error);\n            setError('Ha ocurrido un error al guardar las flashcards. Por favor, inténtalo de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNextCard = ()=>{\n        if (activeIndex < flashcardsGeneradas.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    const handlePrevCard = ()=>{\n        if (activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    const toggleRespuesta = ()=>{\n        setMostrarRespuesta(!mostrarRespuesta);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-8 border-t pt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold mb-4\",\n                children: \"Generador de Flashcards\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmitForm(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"peticion\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Describe las flashcards que deseas generar:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"peticion\",\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                rows: 3,\n                                ...register('peticion'),\n                                placeholder: \"Ej: Genera flashcards sobre los conceptos principales del tema 1\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            errors.peticion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.peticion.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"La IA generar\\xe1 flashcards basadas en los documentos seleccionados y tu petici\\xf3n.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                            disabled: isLoading || documentosSeleccionados.length === 0,\n                            children: isLoading ? 'Generando...' : 'Generar Flashcards'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Generando flashcards, por favor espera...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this),\n            flashcardsGeneradas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: [\n                            \"Flashcards generadas (\",\n                            flashcardsGeneradas.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionGuardada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Guardar flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tipoColeccion\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"\\xbfD\\xf3nde quieres guardar estas flashcards?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"nuevaColeccion\",\n                                                        name: \"tipoColeccion\",\n                                                        value: \"nueva\",\n                                                        checked: coleccionSeleccionada === 'nueva',\n                                                        onChange: ()=>setColeccionSeleccionada('nueva'),\n                                                        className: \"mr-2\",\n                                                        disabled: isLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"nuevaColeccion\",\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"Crear nueva colecci\\xf3n\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"coleccionExistente\",\n                                                        name: \"tipoColeccion\",\n                                                        value: \"existente\",\n                                                        checked: coleccionSeleccionada !== 'nueva',\n                                                        onChange: ()=>{\n                                                            // Seleccionar la primera colección por defecto si hay alguna\n                                                            if (coleccionesExistentes.length > 0) {\n                                                                setColeccionSeleccionada(coleccionesExistentes[0].id);\n                                                            } else {\n                                                                setColeccionSeleccionada('');\n                                                            }\n                                                        },\n                                                        className: \"mr-2\",\n                                                        disabled: isLoading || coleccionesExistentes.length === 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"coleccionExistente\",\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"A\\xf1adir a una colecci\\xf3n existente\",\n                                                            coleccionesExistentes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 ml-2\",\n                                                                children: \"(No hay colecciones disponibles)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            coleccionSeleccionada === 'nueva' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tituloColeccion\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"T\\xedtulo de la nueva colecci\\xf3n:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"tituloColeccion\",\n                                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                value: tituloColeccion,\n                                                onChange: (e)=>setTituloColeccion(e.target.value),\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"descripcionColeccion\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Descripci\\xf3n (opcional):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"descripcionColeccion\",\n                                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                rows: 2,\n                                                value: descripcionColeccion,\n                                                onChange: (e)=>setDescripcionColeccion(e.target.value),\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 17\n                            }, this),\n                            coleccionSeleccionada !== 'nueva' && coleccionesExistentes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"coleccionExistenteSelect\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Selecciona una colecci\\xf3n:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"coleccionExistenteSelect\",\n                                        className: \"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        value: coleccionSeleccionada,\n                                        onChange: (e)=>setColeccionSeleccionada(e.target.value),\n                                        disabled: isLoading,\n                                        children: coleccionesExistentes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: coleccion.id,\n                                                children: coleccion.titulo\n                                            }, coleccion.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleGuardarColeccion,\n                                    className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                    disabled: isLoading,\n                                    children: isLoading ? 'Guardando...' : 'Guardar flashcards'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, this),\n                    coleccionGuardada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-100 text-green-800 p-4 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: coleccionSeleccionada === 'nueva' ? '¡Nueva colección creada correctamente!' : '¡Flashcards añadidas a la colección correctamente!'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    \"Puedes acceder a \",\n                                    coleccionSeleccionada === 'nueva' ? 'ella' : 'las flashcards',\n                                    ' desde la secci\\xf3n de \"Mis Flashcards\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg shadow-md p-6 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevCard,\n                                        disabled: activeIndex === 0,\n                                        className: \"p-2 rounded-full \".concat(activeIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            activeIndex + 1,\n                                            \" de \",\n                                            flashcardsGeneradas.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextCard,\n                                        disabled: activeIndex === flashcardsGeneradas.length - 1,\n                                        className: \"p-2 rounded-full \".concat(activeIndex === flashcardsGeneradas.length - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-[200px] flex items-center justify-center cursor-pointer\",\n                                onClick: toggleRespuesta,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 w-full\",\n                                    children: !mostrarRespuesta ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-lg\",\n                                        children: flashcardsGeneradas[activeIndex].pregunta\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold text-lg mb-2\",\n                                                children: flashcardsGeneradas[activeIndex].pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4 text-left whitespace-pre-wrap\",\n                                                children: flashcardsGeneradas[activeIndex].respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleRespuesta,\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                    children: mostrarRespuesta ? 'Ocultar respuesta' : 'Mostrar respuesta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Todas las flashcards:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: flashcardsGeneradas.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 \".concat(index === activeIndex ? 'border-blue-500 bg-blue-50' : ''),\n                                        onClick: ()=>{\n                                            setActiveIndex(index);\n                                            setMostrarRespuesta(false);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: card.pregunta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\FlashcardGenerator.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardGenerator, \"M6/UsnyCJhBHhxoCe2rxHpLwfkY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm\n    ];\n});\n_c = FlashcardGenerator;\nvar _c;\n$RefreshReg$(_c, \"FlashcardGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FlashcardGenerator.tsx\n"));

/***/ })

});