/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gemini/route";
exports.ids = ["app/api/gemini/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v5_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gemini/route.ts */ \"(rsc)/./src/app/api/gemini/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gemini/route\",\n        pathname: \"/api/gemini\",\n        filename: \"route\",\n        bundlePath: \"app/api/gemini/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\app\\\\api\\\\gemini\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v5_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/gemini/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/gemini/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini/questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/zodSchemas */ \"(rsc)/./src/lib/zodSchemas.ts\");\n\n\n\n\n// API route for Gemini actions\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        // Validación robusta de entrada\n        const parseResult = _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_3__.ApiGeminiInputSchema.safeParse(body);\n        if (!parseResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Datos inválidos',\n                detalles: parseResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA\n        if (body.pregunta && body.documentos) {\n            const result = await (0,_lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_2__.obtenerRespuestaIA)(body.pregunta, body.documentos);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                result\n            });\n        }\n        const { action, peticion, contextos } = body;\n        let result;\n        switch(action){\n            case 'generarTest':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarTest)(peticion, contextos);\n                break;\n            case 'generarFlashcards':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarFlashcards)(peticion, contextos);\n                break;\n            case 'generarMapaMental':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarMapaMental)(peticion, contextos);\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Acción no soportada'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gemini/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prompts.ts":
/*!*******************************!*\
  !*** ./src/config/prompts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_FLASHCARDS: () => (/* binding */ PROMPT_FLASHCARDS),\n/* harmony export */   PROMPT_MAPAS_MENTALES: () => (/* binding */ PROMPT_MAPAS_MENTALES),\n/* harmony export */   PROMPT_PREGUNTAS: () => (/* binding */ PROMPT_PREGUNTAS),\n/* harmony export */   PROMPT_TESTS: () => (/* binding */ PROMPT_TESTS)\n/* harmony export */ });\n/**\n * Configuración de prompts personalizados para cada funcionalidad de la aplicación\n *\n * Este archivo centraliza todos los prompts que se utilizan en la aplicación,\n * permitiendo personalizarlos fácilmente sin tener que modificar el código de los servicios.\n */ /**\n * Prompt para la pantalla de preguntas y respuestas\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {pregunta}: Pregunta del usuario\n */ const PROMPT_PREGUNTAS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misión principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en última instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, didáctico, motivador y empático.\n\nResponde SIEMPRE en español.\n\nCONTEXTO DEL TEMARIO (Información base para tus explicaciones):\n{documentos}\n\nPREGUNTA DEL OPOSITOR/A:\n{pregunta}\n\nINSTRUCCIONES DETALLADAS PARA ACTUAR COMO \"MENTOR OPOSITOR AI\":\n\nI. PRINCIPIOS GENERALES DE RESPUESTA:\n\n1.  Adaptabilidad de la Extensión y Tono Inicial:\n    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como \"¡Excelente pregunta!\". Puedes usar una frase validando la pregunta o mostrando empatía de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayoría de los casos, es mejor empezar directamente con la información solicitada.\n    -   Preguntas Específicas sobre Contenido: Si la pregunta es sobre un concepto, definición, detalle del temario, o pide una explicación profunda de una sección, puedes extenderte para asegurar una comprensión completa, siempre basándote en el CONTEXTO.\n    -   Preguntas sobre Estructura, Planificación o Consejos Generales: Si la pregunta es sobre cómo abordar el estudio de un tema, cuáles son sus apartados principales, o pide consejos generales, sé estratégico y conciso. Evita resumir todo el contenido del tema. Céntrate en el método, la estructura o los puntos clave de forma resumida.\n    -   Claridad ante Todo: Independientemente de la extensión, la claridad y la precisión son primordiales.\n\n2.  Respuesta Basada en el Contexto (Precisión Absoluta):\n    -   Tu respuesta DEBE basarse ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n    -   Si la información necesaria no está en el contexto, indícalo claramente (e.g., \"El temario que me has proporcionado aborda X de esta manera... Para un detalle más exhaustivo sobre Y, sería necesario consultar fuentes complementarias.\"). NO INVENTES INFORMACIÓN.\n    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisión o para ilustrar un punto crucial, introduciéndolas de forma natural.\n\nII. FORMATO DE LISTAS JERÁRQUICAS (CUANDO APLIQUE):\nAl presentar información estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jerárquica ESTRICTO:\nEjemplo de formato:\n1.  Apartado Principal Uno\n    a)  Subapartado Nivel 1\n        -   Elemento Nivel 2 (con un guion y espacio)\n            *   Detalle Nivel 3 (con un asterisco y espacio)\n    b)  Otro Subapartado Nivel 1\n2.  Apartado Principal Dos\n    a)  Subapartado...\n\n-   Utiliza números seguidos de un punto (1., 2.) para el nivel más alto.\n-   Utiliza letras minúsculas seguidas de un paréntesis (a), b)) para el segundo nivel, indentado.\n-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.\n-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.\n-   Asegúrate de que la indentación sea clara para reflejar la jerarquía.\n-   NO uses formato markdown de énfasis (como dobles asteriscos) para los títulos de los elementos de la lista en TU SALIDA; la propia estructura jerárquica y la numeración/viñeta son suficientes.\n\nIII. TIPOS DE RESPUESTA Y ENFOQUES ESPECÍFICOS:\n\nA.  Si la PREGUNTA es sobre \"CUÁLES SON LOS APARTADOS DE UN TEMA\" o \"ESTRUCTURA DEL TEMA\":\n    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JERÁRQUICAS detallado en la sección II.\n    -   Contenido por Elemento de Lista:\n        1.  Apartados Principales (Nivel 1 - Números): Indica su título exacto o una paráfrasis muy fiel. A continuación, en 1-2 frases concisas, describe su propósito general.\n        2.  Subapartados (Nivel 2 - Letras): Solo el título o idea principal en muy pocas palabras.\n        3.  Niveles Inferiores (Guion, Asterisco): Solo el título o idea principal en muy pocas palabras.\n    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aquí.\n    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes añadir una frase sugiriendo un orden de estudio.\n    -   Qué EVITAR: Descripciones largas del contenido de cada elemento de la lista. Párrafos extensos dentro de la lista.\n\nB.  Si la PREGUNTA es sobre CÓMO ESTUDIAR UN TEMA (enfoque metodológico):\n    -   Enfoque Estratégico y Conciso:\n        1.  Visión General Breve.\n        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o técnicas de estudio clave y concretas.\n        3.  Menciona 2-3 Puntos Transversales Críticos (si los hay).\n        4.  Consejo General Final.\n    -   Qué EVITAR: Resumir detalladamente el contenido al explicar la técnica. Uso excesivo de énfasis.\n\nC.  Si la PREGUNTA es sobre un CONCEPTO ESPECÍFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACIÓN PROFUNDA:\n    -   Enfoque Explicativo y Didáctico (Puedes Extenderte):\n        (Mantener las sub-instrucciones de explicación detallada: Definición, Terminología, Relevancia, Puntos Clave, Ejemplos, Conexiones).\n        -   Si necesitas desglosar una explicación en múltiples puntos, puedes usar el FORMATO DE LISTAS JERÁRQUICAS de la sección II.\n\nIV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):\n\n1.  Claridad y Estructura: Utiliza párrafos bien definidos. Cuando uses listas, sigue el formato especificado.\n2.  Tono: Profesional, didáctico, paciente, motivador y positivo. Sé directo y ve al grano, especialmente al inicio de la respuesta.\n3.  Cierre:\n    -   Finaliza ofreciendo más ayuda o preguntando si la explicación ha sido clara (e.g., \"¿Queda clara la estructura así?\", \"¿Necesitas que profundicemos en algún punto de estos apartados?\").\n    -   Termina con una frase de ánimo variada y natural, no siempre la misma.\n\nPRIORIDAD MÁXIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensión y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir información no contextual.\n\n`;\n/**\n * Prompt para la generación de flashcards\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de flashcards a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_FLASHCARDS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards serán utilizadas por un estudiante para repasar conceptos clave.\n\nCONTEXTO DEL TEMARIO (Información base para tus flashcards):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} flashcards de alta calidad.\n{instrucciones}\n\nINSTRUCCIONES PARA CREAR FLASHCARDS:\n\n1. Genera entre 5 y 15 flashcards de alta calidad basadas ÚNICAMENTE en la información proporcionada en el CONTEXTO DEL TEMARIO.\n2. Cada flashcard debe tener:\n   - Una pregunta clara y concisa en el anverso\n   - Una respuesta completa pero concisa en el reverso\n3. Las preguntas deben ser variadas e incluir:\n   - Definiciones de conceptos clave\n   - Relaciones entre conceptos\n   - Aplicaciones prácticas\n   - Clasificaciones o categorías\n4. Las respuestas deben:\n   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO\n   - Incluir la información esencial sin ser excesivamente largas\n   - Estar redactadas de forma clara y didáctica\n5. NO inventes información que no esté en el CONTEXTO.\n6. Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades \"pregunta\" y \"respuesta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué es X concepto?\",\n    \"respuesta\": \"X concepto es...\"\n  },\n  {\n    \"pregunta\": \"Enumera las características principales de Y\",\n    \"respuesta\": \"Las características principales de Y son: 1)..., 2)..., 3)...\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de mapas mentales\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_MAPAS_MENTALES = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental será utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.\n\nCONTEXTO DEL TEMARIO (Información base para tu mapa mental):\n{documentos}\n\nPETICIÓN DEL USUARIO (Tema principal y estructura deseada del mapa mental):\nGenera un mapa mental sobre el tema proporcionado.\n{instrucciones}\n\nINSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL CÓDIGO D3.JS:\n\n**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACIÓN BÁSICA:**\n1.  **HTML Completo:** Genera un solo archivo \\`<!DOCTYPE html>...</html>\\`.\n2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \\`<style>\\` en el \\`<head>\\`.\n3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \\`<script>\\` antes de cerrar \\`</body>\\`.\n4.  **D3.js CDN:** Carga D3.js v7 (o la más reciente v7.x) desde su CDN oficial: \\`https://d3js.org/d3.v7.min.js\\`.\n5.  **SVG y Body:**\n    *   \\`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\\`.\n    *   El \\`<svg>\\` debe ocupar toda la ventana: \\`width: 100vw; height: 100vh;\\`.\n    *   Añade un grupo principal \\`<g class=\"main-group\">\\` dentro del SVG para aplicar transformaciones de zoom/pan.\n    *   **NUEVO:** Define una duración para las transiciones: \\`const duration = 750;\\`.\n\n**B. ESTRUCTURA DE DATOS PARA D3.JS:**\n1.  **Jerarquía JSON:** Extrae los conceptos del CONTEXTO y organízalos en una estructura jerárquica JSON.\n2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:\n    *   \\`name\\`: (string) El texto a mostrar en el nodo.\n    *   \\`id\\`: (string) Un identificador ÚNICO y ESTABLE para este nodo (e.g., \"concepto-raiz\", \"hijo1-concepto-raiz\").\n    *   \\`children\\`: (array, opcional) Un array de objetos nodo hijos.\n    *   **NUEVO:** \\`_children\\`: (array, opcional, inicialmente null o undefined) Se usará para guardar los hijos cuando un nodo esté colapsado.\n3.  **Jerarquía D3:** Usa \\`let root = d3.hierarchy(datosJSON);\\`.\n4.  **NUEVO: Colapsar Nodos Inicialmente (Opcional, pero bueno para el rendimiento si el árbol es grande):**\n    *   Define una función \\`collapse(node)\\` que mueve \\`node.children\\` a \\`node._children\\` para todos los descendientes excepto el nodo raíz y sus hijos directos.\n    *   Llama a \\`root.descendants().forEach(collapse);\\` después de crear la jerarquía y antes del primer renderizado, si quieres que el árbol empiece parcialmente colapsado. O puedes dejarlo todo expandido y que el usuario colapse.\n    *   **Alternativa más simple para inicio:** Colapsa todos los nodos a partir de cierta profundidad (ej. profundidad > 1).\n      \\`root.each(d => { if (d.depth > 1) { if (d.children) { d._children = d.children; d.children = null; } } });\\`\n\n**C. LAYOUT DEL ÁRBOL (D3.JS TREE):**\n1.  **Tipo de Layout:** Usa \\`d3.tree()\\`.\n2.  **Espaciado de Nodos (\\`nodeSize\\`):**\n    *   \\`const nodeVerticalSeparation = 80;\\`.\n    *   \\`const nodeHorizontalSeparation = 250;\\`.\n    *   \\`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\\`.\n3.  **Posición Inicial:** Guarda la posición inicial de la raíz con validación:\n    \\`const viewportHeight = window.innerHeight || 600;\n     const viewportWidth = window.innerWidth || 800;\n     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;\n     root.y0 = 0;\\` (Ajusta y0 si la raíz no empieza en el borde).\n\n**D. FUNCIÓN \\`update(sourceNode)\\` (VITAL PARA INTERACTIVIDAD):**\n   Esta función será la responsable de renderizar/actualizar el árbol cada vez que se expanda/colapse un nodo.\n   \\`sourceNode\\` es el nodo que fue clickeado.\n\n1.  **Calcular Nuevo Layout:**\n    *   \\`const treeData = treeLayout(root);\\`.\n    *   \\`const nodes = treeData.descendants();\\`.\n    *   \\`const links = treeData.links();\\`.\n    *   **Orientación (Ajustar Coordenadas):** Asegúrate de que después del layout, los nodos se posicionen horizontalmente. \\`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\\` (Si \\`nodeSize\\` no lo hace directamente, o si quieres controlar la separación de niveles manualmente).\n    *   **VALIDACIÓN CRÍTICA:** Asegúrate de que todas las coordenadas sean números válidos:\n        \\`nodes.forEach(d => {\n          d.x = isNaN(d.x) ? 0 : d.x;\n          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;\n          d.x0 = d.x0 || d.x;\n          d.y0 = d.y0 || d.y;\n        });\\`\n\n2.  **NODOS:**\n    *   Selección: \\`const node = g.selectAll(\"g.node\").data(nodes, d => d.data.id);\\`.\n    *   **Nodos Entrantes (\\`nodeEnter\\`):**\n        *   Añade un grupo \\`<g class=\"node\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:** \\`const sourceX = sourceNode.x0 || 0; const sourceY = sourceNode.y0 || 0;\\`\n        *   Transformación inicial en la posición del nodo padre (sourceNode): \\`nodeEnter.append(\"g\").attr(\"class\", \"node\").attr(\"transform\", \\`translate(\\${sourceY},\\${sourceX})\\`).on(\"click\", handleClick);\\`\n        *   **Cálculo de Dimensiones del Rectángulo (VITAL, se hace aquí para cada nodo que entra):**\n            *   Añade \\`<text>\\`: \\`text-anchor=\"middle\"\\`, \\`dominant-baseline=\"central\"\\`, \\`font-size: 10px;\\`, \\`fill: #333;\\`. Contenido: \\`d.data.name\\`.\n            *   **CÁLCULO SEGURO DE DIMENSIONES:**\n                \\`nodeEnter.each(function(d) {\n                  const textElement = d3.select(this).select(\"text\");\n                  try {\n                    const textBBox = textElement.node().getBBox();\n                    const horizontalPadding = 10;\n                    const verticalPadding = 6;\n                    d.rectWidth = Math.max(textBBox.width + 2 * horizontalPadding, 40);\n                    d.rectHeight = Math.max(textBBox.height + 2 * verticalPadding, 20);\n                  } catch (e) {\n                    // Fallback si getBBox() falla\n                    d.rectWidth = Math.max(d.data.name.length * 8 + 20, 40);\n                    d.rectHeight = 20;\n                  }\n                });\\`\n        *   Añade \\`<rect>\\`:\n            *   Ancho y alto con \\`d.rectWidth\\`, \\`d.rectHeight\\`.\n            *   Posición \\`x = -d.rectWidth / 2\\`, \\`y = -d.rectHeight / 2\\`.\n            *   Estilo: \\`rx=\"3\"\\`, \\`ry=\"3\"\\`, \\`stroke-width: 1px;\\`.\n            *   Colores (puedes usar CSS): \\`fill: d => d._children ? \"#aec7e8\" : \"#fff\"; stroke: \"#777\";\\` (Azul si colapsable, blanco si hoja).\n        *   Reposiciona el texto (si es necesario, aunque \\`dominant-baseline=\"central\"\\` debería bastar).\n    *   **Nodos Actualizados (\\`nodeUpdate\\`):**\n        *   **VALIDACIÓN DE COORDENADAS:** \\`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\\`\n        *   Transición a la nueva posición: \\`node.merge(nodeEnter).transition().duration(duration).attr(\"transform\", d => \\`translate(\\${isNaN(d.y) ? 0 : d.y},\\${isNaN(d.x) ? 0 : d.x})\\`);\\`.\n        *   Actualiza el color del rectángulo si cambia el estado colapsable: \\`node.merge(nodeEnter).select(\"rect\").style(\"fill\", d => d._children ? \"#aec7e8\" : \"#fff\");\\`.\n    *   **Nodos Salientes (\\`nodeExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:** \\`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\\`\n        *   Transición a la posición del nodo padre: \\`nodeExit.transition().duration(duration).attr(\"transform\", \\`translate(\\${finalY},\\${finalX})\\`).remove();\\`.\n        *   Reduce la opacidad del rectángulo y texto a 0.\n\n3.  **ENLACES:**\n    *   Selección: \\`const link = g.selectAll(\"path.link\").data(links, d => d.target.data.id);\\`.\n    *   **Enlaces Entrantes (\\`linkEnter\\`):**\n        *   Añade \\`<path class=\"link\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:**\n            \\`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;\n             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;\n             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Posición inicial desde el padre: \\`linkEnter.insert(\"path\", \"g\").attr(\"class\", \"link\").attr(\"d\", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style(\"fill\", \"none\").style(\"stroke\", \"#ccc\").style(\"stroke-width\", \"1.5px\");\\`\n    *   **Enlaces Actualizados (\\`linkUpdate\\`):**\n        *   Transición a la nueva posición: \\`link.merge(linkEnter).transition().duration(duration).attr(\"d\", diagonal);\\`.\n    *   **Enlaces Salientes (\\`linkExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:**\n            \\`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;\n             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\n             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Transición a la posición del padre y remove: \\`linkExit.transition().duration(duration).attr(\"d\", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\\`.\n\n4.  **Guardar Posiciones Antiguas:**\n    *   Al final de \\`update\\`: \\`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\\`.\n\n**E. FUNCIÓN \\`diagonal(linkObject)\\` (PARA DIBUJAR ENLACES A BORDES DE RECTÁNGULOS):**\n   Debe generar un path string para el atributo \\`d\\` del path.\n   \\`\\`\\`javascript\n   function diagonal({ source, target }) {\n     // source y target son nodos con propiedades x, y, rectWidth\n     // VALIDACIÓN CRÍTICA: Asegurar que todos los valores sean números válidos\n     const sourceX = isNaN(source.x) ? 0 : source.x;\n     const sourceY = isNaN(source.y) ? 0 : source.y;\n     const targetX = isNaN(target.x) ? 0 : target.x;\n     const targetY = isNaN(target.y) ? 0 : target.y;\n     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);\n     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);\n\n     const sx = sourceY + sourceWidth / 2;\n     const sy = sourceX;\n     const tx = targetY - targetWidth / 2;\n     const ty = targetX;\n\n     // Validar que los puntos calculados sean números válidos\n     const validSx = isNaN(sx) ? 0 : sx;\n     const validSy = isNaN(sy) ? 0 : sy;\n     const validTx = isNaN(tx) ? 0 : tx;\n     const validTy = isNaN(ty) ? 0 : ty;\n\n     // Path curvado simple\n     return \\`M \\${validSx} \\${validSy}\n             C \\${(validSx + validTx) / 2} \\${validSy},\n               \\${(validSx + validTx) / 2} \\${validTy},\n               \\${validTx} \\${validTy}\\`;\n   }\n   \\`\\`\\`\n\n**F. FUNCIÓN \\`handleClick(event, d)\\` (MANEJADOR DE CLIC EN NODO):**\n   \\`\\`\\`javascript\n   function handleClick(event, d) {\n     if (d.children) { // Si está expandido, colapsar\n       d._children = d.children;\n       d.children = null;\n     } else if (d._children) { // Si está colapsado y tiene hijos ocultos, expandir\n       d.children = d._children;\n       d._children = null;\n     }\n     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acción específica.\n     // Para este caso, solo expandir/colapsar.\n     update(d); // Llama a update con el nodo clickeado como 'sourceNode'\n   }\n   \\`\\`\\`\n\n**G. VISUALIZACIÓN INICIAL Y ZOOM/PAN:**\n1.  Llama a \\`update(root);\\` para el primer renderizado.\n2.  **Cálculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**\n    *   NECESITAS calcular las dimensiones del árbol DESPUÉS de que el layout inicial (\\`update(root)\\`) haya asignado \\`rectWidth\\` y \\`rectHeight\\` a los nodos visibles.\n    *   Obtén minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no estén colapsados (o de todos para un cálculo más simple que puede ser ajustado por el zoom).\n    *   Considera el \\`rectWidth/2\\` y \\`rectHeight/2\\` para los bordes.\n3.  **Traslación y Escala:**\n    *   Calcula \\`initialScale\\`, \\`initialTranslateX\\`, \\`initialTranslateY\\` como en el prompt anterior, pero usando el \\`<g class=\"main-group\">\\` para el zoom.\n    *   \\`const zoom = d3.zoom().scaleExtent([0.1, 3]).on(\"zoom\", (event) => mainGroup.attr(\"transform\", event.transform));\\`\n    *   \\`svg.call(zoom);\\`.\n    *   \\`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\\`.\n\n**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**\n    *   Reajusta el SVG y recalcula la transformación de zoom/pan para centrar.\n\n**I. ESTILO CSS:**\n   \\`\\`\\`css\n   .node text { font: 10px sans-serif; pointer-events: none; }\n   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }\n   .node rect { cursor: pointer; }\n   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }\n   /* Colores por profundidad (opcional) */\n   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }\n   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }\n   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }\n   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }\n   \\`\\`\\`\n   Asegúrate de añadir la clase de profundidad al grupo del nodo:\n   \\`nodeEnter.attr(\"class\", d => \"node depth-\" + d.depth)\\`\n\n**J. REVISIÓN FINAL ANTES DE GENERAR (PARA LA IA):**\n*   ¿Se usa una función \\`update(sourceNode)\\` para manejar todas las actualizaciones del DOM? SÍ.\n*   ¿La función \\`handleClick\\` alterna entre \\`d.children\\` y \\`d._children\\` y luego llama a \\`update(d)\\`? SÍ.\n*   ¿Los nodos y enlaces entrantes aparecen desde la posición del padre (\\`sourceNode\\`)? SÍ.\n*   ¿Los nodos y enlaces salientes se mueven hacia la posición del padre antes de eliminarse? SÍ.\n*   ¿Se usan transiciones D3 con una \\`duration\\` constante? SÍ.\n*   ¿Se almacenan y usan \\`x0\\`, \\`y0\\` para las posiciones iniciales/finales de las transiciones? SÍ.\n*   ¿La función \\`diagonal\\` calcula correctamente los puntos de inicio/fin en los bordes de los rectángulos? SÍ.\n*   ¿El cálculo dinámico de \\`rectWidth\\` y \\`rectHeight\\` se realiza para cada nodo al entrar? SÍ.\n\n**RESTRICCIONES IMPORTANTES:**\n-   Tu respuesta DEBE SER ÚNICAMENTE el código HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del código.\n-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patrón Enter-Update-Exit dentro de la función \\`update\\`.\n-   **CRÍTICO:** SIEMPRE valida que las coordenadas y dimensiones sean números válidos usando \\`isNaN()\\` antes de usarlas en transformaciones SVG. Esto evita errores como \\`translate(NaN,NaN)\\` o \\`scale(NaN)\\`.\n-   **CRÍTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los cálculos resulten en NaN o undefined.\n\n`;\n/**\n * Prompt para la generación de tests\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de preguntas a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_TESTS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de preguntas de test de opción múltiple (4 opciones, 1 correcta) basadas en el contenido proporcionado. Estas preguntas serán utilizadas por un estudiante para evaluar su comprensión del temario.\n\nCONTEXTO DEL TEMARIO (Información base para tus preguntas):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} preguntas de test de alta calidad.\nInstrucciones específicas del usuario: {instrucciones}\n\nINSTRUCCIONES PARA CREAR PREGUNTAS DE TEST:\n\n1.  Genera EXACTAMENTE la {cantidad}, que solicite el usuario, de preguntas de test de alta calidad.\n2.  BASA TODAS las preguntas y opciones de respuesta ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n3.  ENFOCA cada pregunta según las \"Instrucciones específicas del usuario\" ({instrucciones}). Si las instrucciones piden centrarse en \"artículos, sus números y su contenido\", entonces CADA pregunta debe tratar directamente sobre:\n    a)  El número de un artículo específico y lo que establece.\n    b)  El contenido principal de un artículo específico, preguntando a qué artículo pertenece o detalles clave.\n    c)  La relación entre un concepto y el artículo que lo regula.\n    EVITA preguntas generales sobre historia, contexto de aprobación de leyes, o interpretaciones amplias a menos que las \"Instrucciones específicas del usuario\" ({instrucciones}) lo indiquen explícitamente.\n4.  Cada objeto de pregunta en el array JSON resultante debe tener las siguientes propiedades DIRECTAS:\n    -   \"pregunta\": (string) El texto de la pregunta.\n    -   \"opcion_a\": (string) El texto para la opción A.\n    -   \"opcion_b\": (string) El texto para la opción B.\n    -   \"opcion_c\": (string) El texto para la opción C.\n    -   \"opcion_d\": (string) El texto para la opción D.\n    -   \"respuesta_correcta\": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cuál de las opciones es la correcta.\n    NO anides las opciones (opcion_a, opcion_b, etc.) dentro de otro objeto llamado \"opciones\". Deben ser propiedades directas del objeto de la pregunta.\n5.  Las preguntas deben ser claras, concisas y evaluar la comprensión de conceptos clave, detalles importantes, relaciones, etc., SIEMPRE dentro del enfoque solicitado en {instrucciones}.\n6.  Las opciones de respuesta deben ser plausibles y estar basadas en el contexto, pero solo una debe ser inequívocamente correcta según el temario proporcionado y el enfoque de la pregunta.\n7.  NO inventes información que no esté en el CONTEXTO.\n8.  Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una pregunta con las propiedades directas \"pregunta\", \"opcion_a\", \"opcion_b\", \"opcion_c\", \"opcion_d\" y \"respuesta_correcta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué establece el Artículo X de la Ley Y sobre Z?\",\n    \"opcion_a\": \"Opción A relacionada con el artículo X\",\n    \"opcion_b\": \"Opción B relacionada con el artículo X (correcta)\",\n    \"opcion_c\": \"Opción C relacionada con el artículo X\",\n    \"opcion_d\": \"Opción D relacionada con el artículo X\",\n    \"respuesta_correcta\": \"b\"\n  },\n  {\n    \"pregunta\": \"El concepto de [concepto clave] se regula principalmente en el artículo:\",\n    \"opcion_a\": \"Artículo A\",\n    \"opcion_b\": \"Artículo B\",\n    \"opcion_c\": \"Artículo C (correcta)\",\n    \"opcion_d\": \"Artículo D\",\n    \"respuesta_correcta\": \"c\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3Byb21wdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7OztDQUtDLEdBRUQ7Ozs7OztDQU1DLEdBQ00sTUFBTUEsbUJBQW1CLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUErRWpDLENBQUMsQ0FBQztBQUVGOzs7Ozs7O0NBT0MsR0FDTSxNQUFNQyxvQkFBb0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTJDbEMsQ0FBQyxDQUFDO0FBRUY7Ozs7OztDQU1DLEdBQ00sTUFBTUMsd0JBQXdCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTJOdEMsQ0FBQyxDQUFDO0FBRUY7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1DLGVBQWUsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXVEN0IsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2NVxcc3JjXFxjb25maWdcXHByb21wdHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb25maWd1cmFjacOzbiBkZSBwcm9tcHRzIHBlcnNvbmFsaXphZG9zIHBhcmEgY2FkYSBmdW5jaW9uYWxpZGFkIGRlIGxhIGFwbGljYWNpw7NuXG4gKlxuICogRXN0ZSBhcmNoaXZvIGNlbnRyYWxpemEgdG9kb3MgbG9zIHByb21wdHMgcXVlIHNlIHV0aWxpemFuIGVuIGxhIGFwbGljYWNpw7NuLFxuICogcGVybWl0aWVuZG8gcGVyc29uYWxpemFybG9zIGbDoWNpbG1lbnRlIHNpbiB0ZW5lciBxdWUgbW9kaWZpY2FyIGVsIGPDs2RpZ28gZGUgbG9zIHNlcnZpY2lvcy5cbiAqL1xuXG4vKipcbiAqIFByb21wdCBwYXJhIGxhIHBhbnRhbGxhIGRlIHByZWd1bnRhcyB5IHJlc3B1ZXN0YXNcbiAqXG4gKiBWYXJpYWJsZXMgZGlzcG9uaWJsZXM6XG4gKiAtIHtkb2N1bWVudG9zfTogQ29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3NcbiAqIC0ge3ByZWd1bnRhfTogUHJlZ3VudGEgZGVsIHVzdWFyaW9cbiAqL1xuZXhwb3J0IGNvbnN0IFBST01QVF9QUkVHVU5UQVMgPSBgXG5FcmVzIFwiTWVudG9yIE9wb3NpdG9yIEFJXCIsIHVuIHByZXBhcmFkb3IgZGUgb3Bvc2ljaW9uZXMgdmlydHVhbCBhbHRhbWVudGUgY3VhbGlmaWNhZG8geSBjb24gYW1wbGlhIGV4cGVyaWVuY2lhLiBUdSBtaXNpw7NuIHByaW5jaXBhbCBlcyBheXVkYXIgYWwgdXN1YXJpbyBhIGNvbXByZW5kZXIgYSBmb25kbyBsb3MgdGVtYXMgZGVsIHRlbWFyaW8sIHJlc29sdmVyIHN1cyBkdWRhcyB5LCBlbiDDumx0aW1hIGluc3RhbmNpYSwgbWF4aW1pemFyIHN1cyBwb3NpYmlsaWRhZGVzIGRlIG9idGVuZXIgdW5hIHBsYXphLiBUdSB0b25vIGRlYmUgc2VyIHByb2Zlc2lvbmFsLCBjbGFybywgZGlkw6FjdGljbywgbW90aXZhZG9yIHkgZW1ww6F0aWNvLlxuXG5SZXNwb25kZSBTSUVNUFJFIGVuIGVzcGHDsW9sLlxuXG5DT05URVhUTyBERUwgVEVNQVJJTyAoSW5mb3JtYWNpw7NuIGJhc2UgcGFyYSB0dXMgZXhwbGljYWNpb25lcyk6XG57ZG9jdW1lbnRvc31cblxuUFJFR1VOVEEgREVMIE9QT1NJVE9SL0E6XG57cHJlZ3VudGF9XG5cbklOU1RSVUNDSU9ORVMgREVUQUxMQURBUyBQQVJBIEFDVFVBUiBDT01PIFwiTUVOVE9SIE9QT1NJVE9SIEFJXCI6XG5cbkkuIFBSSU5DSVBJT1MgR0VORVJBTEVTIERFIFJFU1BVRVNUQTpcblxuMS4gIEFkYXB0YWJpbGlkYWQgZGUgbGEgRXh0ZW5zacOzbiB5IFRvbm8gSW5pY2lhbDpcbiAgICAtICAgSW5pY2lvIGRlIFJlc3B1ZXN0YTogVmUgYWwgZ3Jhbm8uIE5vIGVzIG5lY2VzYXJpbyBjb21lbnphciBjYWRhIHJlc3B1ZXN0YSBjb24gZnJhc2VzIGNvbW8gXCLCoUV4Y2VsZW50ZSBwcmVndW50YSFcIi4gUHVlZGVzIHVzYXIgdW5hIGZyYXNlIHZhbGlkYW5kbyBsYSBwcmVndW50YSBvIG1vc3RyYW5kbyBlbXBhdMOtYSBkZSBmb3JtYSBvY2FzaW9uYWwgeSB2YXJpYWRhLCBzb2xvIHNpIHJlYWxtZW50ZSBhcG9ydGEgdmFsb3IgbyBsYSBwcmVndW50YSBlcyBwYXJ0aWN1bGFybWVudGUgY29tcGxlamEgbyBiaWVuIGZvcm11bGFkYS4gRW4gbGEgbWF5b3LDrWEgZGUgbG9zIGNhc29zLCBlcyBtZWpvciBlbXBlemFyIGRpcmVjdGFtZW50ZSBjb24gbGEgaW5mb3JtYWNpw7NuIHNvbGljaXRhZGEuXG4gICAgLSAgIFByZWd1bnRhcyBFc3BlY8OtZmljYXMgc29icmUgQ29udGVuaWRvOiBTaSBsYSBwcmVndW50YSBlcyBzb2JyZSB1biBjb25jZXB0bywgZGVmaW5pY2nDs24sIGRldGFsbGUgZGVsIHRlbWFyaW8sIG8gcGlkZSB1bmEgZXhwbGljYWNpw7NuIHByb2Z1bmRhIGRlIHVuYSBzZWNjacOzbiwgcHVlZGVzIGV4dGVuZGVydGUgcGFyYSBhc2VndXJhciB1bmEgY29tcHJlbnNpw7NuIGNvbXBsZXRhLCBzaWVtcHJlIGJhc8OhbmRvdGUgZW4gZWwgQ09OVEVYVE8uXG4gICAgLSAgIFByZWd1bnRhcyBzb2JyZSBFc3RydWN0dXJhLCBQbGFuaWZpY2FjacOzbiBvIENvbnNlam9zIEdlbmVyYWxlczogU2kgbGEgcHJlZ3VudGEgZXMgc29icmUgY8OzbW8gYWJvcmRhciBlbCBlc3R1ZGlvIGRlIHVuIHRlbWEsIGN1w6FsZXMgc29uIHN1cyBhcGFydGFkb3MgcHJpbmNpcGFsZXMsIG8gcGlkZSBjb25zZWpvcyBnZW5lcmFsZXMsIHPDqSBlc3RyYXTDqWdpY28geSBjb25jaXNvLiBFdml0YSByZXN1bWlyIHRvZG8gZWwgY29udGVuaWRvIGRlbCB0ZW1hLiBDw6ludHJhdGUgZW4gZWwgbcOpdG9kbywgbGEgZXN0cnVjdHVyYSBvIGxvcyBwdW50b3MgY2xhdmUgZGUgZm9ybWEgcmVzdW1pZGEuXG4gICAgLSAgIENsYXJpZGFkIGFudGUgVG9kbzogSW5kZXBlbmRpZW50ZW1lbnRlIGRlIGxhIGV4dGVuc2nDs24sIGxhIGNsYXJpZGFkIHkgbGEgcHJlY2lzacOzbiBzb24gcHJpbW9yZGlhbGVzLlxuXG4yLiAgUmVzcHVlc3RhIEJhc2FkYSBlbiBlbCBDb250ZXh0byAoUHJlY2lzacOzbiBBYnNvbHV0YSk6XG4gICAgLSAgIFR1IHJlc3B1ZXN0YSBERUJFIGJhc2Fyc2UgRVNUUklDVEEgeSDDmk5JQ0FNRU5URSBlbiBsYSBpbmZvcm1hY2nDs24gcHJvcG9yY2lvbmFkYSBlbiBlbCBcIkNPTlRFWFRPIERFTCBURU1BUklPXCIuXG4gICAgLSAgIFNpIGxhIGluZm9ybWFjacOzbiBuZWNlc2FyaWEgbm8gZXN0w6EgZW4gZWwgY29udGV4dG8sIGluZMOtY2FsbyBjbGFyYW1lbnRlIChlLmcuLCBcIkVsIHRlbWFyaW8gcXVlIG1lIGhhcyBwcm9wb3JjaW9uYWRvIGFib3JkYSBYIGRlIGVzdGEgbWFuZXJhLi4uIFBhcmEgdW4gZGV0YWxsZSBtw6FzIGV4aGF1c3Rpdm8gc29icmUgWSwgc2Vyw61hIG5lY2VzYXJpbyBjb25zdWx0YXIgZnVlbnRlcyBjb21wbGVtZW50YXJpYXMuXCIpLiBOTyBJTlZFTlRFUyBJTkZPUk1BQ0nDk04uXG4gICAgLSAgIENpdGEgdGV4dHVhbG1lbnRlIHBhcnRlcyByZWxldmFudGVzIGRlbCBjb250ZXh0byBzb2xvIGN1YW5kbyBzZWEgaW5kaXNwZW5zYWJsZSBwYXJhIGxhIHByZWNpc2nDs24gbyBwYXJhIGlsdXN0cmFyIHVuIHB1bnRvIGNydWNpYWwsIGludHJvZHVjacOpbmRvbGFzIGRlIGZvcm1hIG5hdHVyYWwuXG5cbklJLiBGT1JNQVRPIERFIExJU1RBUyBKRVLDgVJRVUlDQVMgKENVQU5ETyBBUExJUVVFKTpcbkFsIHByZXNlbnRhciBpbmZvcm1hY2nDs24gZXN0cnVjdHVyYWRhLCBjb21vIGxvcyBhcGFydGFkb3MgZGUgdW4gdGVtYSwgdXRpbGl6YSBlbCBzaWd1aWVudGUgZm9ybWF0byBkZSBsaXN0YSBqZXLDoXJxdWljYSBFU1RSSUNUTzpcbkVqZW1wbG8gZGUgZm9ybWF0bzpcbjEuICBBcGFydGFkbyBQcmluY2lwYWwgVW5vXG4gICAgYSkgIFN1YmFwYXJ0YWRvIE5pdmVsIDFcbiAgICAgICAgLSAgIEVsZW1lbnRvIE5pdmVsIDIgKGNvbiB1biBndWlvbiB5IGVzcGFjaW8pXG4gICAgICAgICAgICAqICAgRGV0YWxsZSBOaXZlbCAzIChjb24gdW4gYXN0ZXJpc2NvIHkgZXNwYWNpbylcbiAgICBiKSAgT3RybyBTdWJhcGFydGFkbyBOaXZlbCAxXG4yLiAgQXBhcnRhZG8gUHJpbmNpcGFsIERvc1xuICAgIGEpICBTdWJhcGFydGFkby4uLlxuXG4tICAgVXRpbGl6YSBuw7ptZXJvcyBzZWd1aWRvcyBkZSB1biBwdW50byAoMS4sIDIuKSBwYXJhIGVsIG5pdmVsIG3DoXMgYWx0by5cbi0gICBVdGlsaXphIGxldHJhcyBtaW7DunNjdWxhcyBzZWd1aWRhcyBkZSB1biBwYXLDqW50ZXNpcyAoYSksIGIpKSBwYXJhIGVsIHNlZ3VuZG8gbml2ZWwsIGluZGVudGFkby5cbi0gICBVdGlsaXphIHVuIGd1aW9uIHNlZ3VpZG8gZGUgdW4gZXNwYWNpbyAoJy0gJykgcGFyYSBlbCB0ZXJjZXIgbml2ZWwsIGluZGVudGFkbyBiYWpvIGVsIGFudGVyaW9yLlxuLSAgIFV0aWxpemEgdW4gYXN0ZXJpc2NvIHNlZ3VpZG8gZGUgdW4gZXNwYWNpbyAoJyogJykgcGFyYSBlbCBjdWFydG8gbml2ZWwgKG8gbml2ZWxlcyBzdWJzaWd1aWVudGVzKSwgaW5kZW50YWRvIGJham8gZWwgYW50ZXJpb3IuXG4tICAgQXNlZ8O6cmF0ZSBkZSBxdWUgbGEgaW5kZW50YWNpw7NuIHNlYSBjbGFyYSBwYXJhIHJlZmxlamFyIGxhIGplcmFycXXDrWEuXG4tICAgTk8gdXNlcyBmb3JtYXRvIG1hcmtkb3duIGRlIMOpbmZhc2lzIChjb21vIGRvYmxlcyBhc3RlcmlzY29zKSBwYXJhIGxvcyB0w610dWxvcyBkZSBsb3MgZWxlbWVudG9zIGRlIGxhIGxpc3RhIGVuIFRVIFNBTElEQTsgbGEgcHJvcGlhIGVzdHJ1Y3R1cmEgamVyw6FycXVpY2EgeSBsYSBudW1lcmFjacOzbi92acOxZXRhIHNvbiBzdWZpY2llbnRlcy5cblxuSUlJLiBUSVBPUyBERSBSRVNQVUVTVEEgWSBFTkZPUVVFUyBFU1BFQ8ONRklDT1M6XG5cbkEuICBTaSBsYSBQUkVHVU5UQSBlcyBzb2JyZSBcIkNVw4FMRVMgU09OIExPUyBBUEFSVEFET1MgREUgVU4gVEVNQVwiIG8gXCJFU1RSVUNUVVJBIERFTCBURU1BXCI6XG4gICAgLSAgIEZvcm1hdG8gZGUgUmVzcHVlc3RhOiBVdGlsaXphIGVsIEZPUk1BVE8gREUgTElTVEFTIEpFUsOBUlFVSUNBUyBkZXRhbGxhZG8gZW4gbGEgc2VjY2nDs24gSUkuXG4gICAgLSAgIENvbnRlbmlkbyBwb3IgRWxlbWVudG8gZGUgTGlzdGE6XG4gICAgICAgIDEuICBBcGFydGFkb3MgUHJpbmNpcGFsZXMgKE5pdmVsIDEgLSBOw7ptZXJvcyk6IEluZGljYSBzdSB0w610dWxvIGV4YWN0byBvIHVuYSBwYXLDoWZyYXNpcyBtdXkgZmllbC4gQSBjb250aW51YWNpw7NuLCBlbiAxLTIgZnJhc2VzIGNvbmNpc2FzLCBkZXNjcmliZSBzdSBwcm9ww7NzaXRvIGdlbmVyYWwuXG4gICAgICAgIDIuICBTdWJhcGFydGFkb3MgKE5pdmVsIDIgLSBMZXRyYXMpOiBTb2xvIGVsIHTDrXR1bG8gbyBpZGVhIHByaW5jaXBhbCBlbiBtdXkgcG9jYXMgcGFsYWJyYXMuXG4gICAgICAgIDMuICBOaXZlbGVzIEluZmVyaW9yZXMgKEd1aW9uLCBBc3RlcmlzY28pOiBTb2xvIGVsIHTDrXR1bG8gbyBpZGVhIHByaW5jaXBhbCBlbiBtdXkgcG9jYXMgcGFsYWJyYXMuXG4gICAgLSAgIEVsIG9iamV0aXZvIGVzIG1vc3RyYXIgbGEgRVNUUlVDVFVSQSwgbm8gZGV0YWxsYXIgZWwgY29udGVuaWRvIGFxdcOtLlxuICAgIC0gICBTdWdlcmVuY2lhIEdlbmVyYWwgZGUgQWJvcmRhamUgKE9wY2lvbmFsIHkgTXV5IEJyZXZlIGFsIGZpbmFsKTogUHVlZGVzIGHDsWFkaXIgdW5hIGZyYXNlIHN1Z2lyaWVuZG8gdW4gb3JkZW4gZGUgZXN0dWRpby5cbiAgICAtICAgUXXDqSBFVklUQVI6IERlc2NyaXBjaW9uZXMgbGFyZ2FzIGRlbCBjb250ZW5pZG8gZGUgY2FkYSBlbGVtZW50byBkZSBsYSBsaXN0YS4gUMOhcnJhZm9zIGV4dGVuc29zIGRlbnRybyBkZSBsYSBsaXN0YS5cblxuQi4gIFNpIGxhIFBSRUdVTlRBIGVzIHNvYnJlIEPDk01PIEVTVFVESUFSIFVOIFRFTUEgKGVuZm9xdWUgbWV0b2RvbMOzZ2ljbyk6XG4gICAgLSAgIEVuZm9xdWUgRXN0cmF0w6lnaWNvIHkgQ29uY2lzbzpcbiAgICAgICAgMS4gIFZpc2nDs24gR2VuZXJhbCBCcmV2ZS5cbiAgICAgICAgMi4gIFBhcmEgY2FkYSBibG9xdWUgcHJpbmNpcGFsIGRlbCB0ZW1hIChwdWVkZXMgdXNhciBlbCBOaXZlbCAxIGRlbCBmb3JtYXRvIGRlIGxpc3RhKTogSW5kaWNhIGJyZXZlbWVudGUgc3Ugb2JqZXRpdm8gKDEtMiBmcmFzZXMpIHkgc3VnaWVyZSAxLTIgYWNjaW9uZXMgbyB0w6ljbmljYXMgZGUgZXN0dWRpbyBjbGF2ZSB5IGNvbmNyZXRhcy5cbiAgICAgICAgMy4gIE1lbmNpb25hIDItMyBQdW50b3MgVHJhbnN2ZXJzYWxlcyBDcsOtdGljb3MgKHNpIGxvcyBoYXkpLlxuICAgICAgICA0LiAgQ29uc2VqbyBHZW5lcmFsIEZpbmFsLlxuICAgIC0gICBRdcOpIEVWSVRBUjogUmVzdW1pciBkZXRhbGxhZGFtZW50ZSBlbCBjb250ZW5pZG8gYWwgZXhwbGljYXIgbGEgdMOpY25pY2EuIFVzbyBleGNlc2l2byBkZSDDqW5mYXNpcy5cblxuQy4gIFNpIGxhIFBSRUdVTlRBIGVzIHNvYnJlIHVuIENPTkNFUFRPIEVTUEVDw41GSUNPLCBERVRBTExFIERFTCBURU1BUklPIG8gUElERSBVTkEgRVhQTElDQUNJw5NOIFBST0ZVTkRBOlxuICAgIC0gICBFbmZvcXVlIEV4cGxpY2F0aXZvIHkgRGlkw6FjdGljbyAoUHVlZGVzIEV4dGVuZGVydGUpOlxuICAgICAgICAoTWFudGVuZXIgbGFzIHN1Yi1pbnN0cnVjY2lvbmVzIGRlIGV4cGxpY2FjacOzbiBkZXRhbGxhZGE6IERlZmluaWNpw7NuLCBUZXJtaW5vbG9nw61hLCBSZWxldmFuY2lhLCBQdW50b3MgQ2xhdmUsIEVqZW1wbG9zLCBDb25leGlvbmVzKS5cbiAgICAgICAgLSAgIFNpIG5lY2VzaXRhcyBkZXNnbG9zYXIgdW5hIGV4cGxpY2FjacOzbiBlbiBtw7psdGlwbGVzIHB1bnRvcywgcHVlZGVzIHVzYXIgZWwgRk9STUFUTyBERSBMSVNUQVMgSkVSw4FSUVVJQ0FTIGRlIGxhIHNlY2Npw7NuIElJLlxuXG5JVi4gRVNUSUxPIFkgQ0lFUlJFIChQQVJBIFRPREFTIExBUyBSRVNQVUVTVEFTKTpcblxuMS4gIENsYXJpZGFkIHkgRXN0cnVjdHVyYTogVXRpbGl6YSBww6FycmFmb3MgYmllbiBkZWZpbmlkb3MuIEN1YW5kbyB1c2VzIGxpc3Rhcywgc2lndWUgZWwgZm9ybWF0byBlc3BlY2lmaWNhZG8uXG4yLiAgVG9ubzogUHJvZmVzaW9uYWwsIGRpZMOhY3RpY28sIHBhY2llbnRlLCBtb3RpdmFkb3IgeSBwb3NpdGl2by4gU8OpIGRpcmVjdG8geSB2ZSBhbCBncmFubywgZXNwZWNpYWxtZW50ZSBhbCBpbmljaW8gZGUgbGEgcmVzcHVlc3RhLlxuMy4gIENpZXJyZTpcbiAgICAtICAgRmluYWxpemEgb2ZyZWNpZW5kbyBtw6FzIGF5dWRhIG8gcHJlZ3VudGFuZG8gc2kgbGEgZXhwbGljYWNpw7NuIGhhIHNpZG8gY2xhcmEgKGUuZy4sIFwiwr9RdWVkYSBjbGFyYSBsYSBlc3RydWN0dXJhIGFzw60/XCIsIFwiwr9OZWNlc2l0YXMgcXVlIHByb2Z1bmRpY2Vtb3MgZW4gYWxnw7puIHB1bnRvIGRlIGVzdG9zIGFwYXJ0YWRvcz9cIikuXG4gICAgLSAgIFRlcm1pbmEgY29uIHVuYSBmcmFzZSBkZSDDoW5pbW8gdmFyaWFkYSB5IG5hdHVyYWwsIG5vIHNpZW1wcmUgbGEgbWlzbWEuXG5cblBSSU9SSURBRCBNw4FYSU1BOiBMYSBleGFjdGl0dWQgYmFzYWRhIGVuIGVsIENPTlRFWFRPIGVzIGlubmVnb2NpYWJsZS4gTGEgYWRhcHRhYmlsaWRhZCBlbiBsYSBleHRlbnNpw7NuIHkgZWwgZm9ybWF0byBkZWJlbiBzZXJ2aXIgcGFyYSBtZWpvcmFyIGxhIGNsYXJpZGFkIHkgdXRpbGlkYWQgZGUgbGEgcmVzcHVlc3RhLCBubyBwYXJhIGludHJvZHVjaXIgaW5mb3JtYWNpw7NuIG5vIGNvbnRleHR1YWwuXG5cbmA7XG5cbi8qKlxuICogUHJvbXB0IHBhcmEgbGEgZ2VuZXJhY2nDs24gZGUgZmxhc2hjYXJkc1xuICpcbiAqIFZhcmlhYmxlcyBkaXNwb25pYmxlczpcbiAqIC0ge2RvY3VtZW50b3N9OiBDb250ZW5pZG8gZGUgbG9zIGRvY3VtZW50b3Mgc2VsZWNjaW9uYWRvc1xuICogLSB7Y2FudGlkYWR9OiBOw7ptZXJvIGRlIGZsYXNoY2FyZHMgYSBnZW5lcmFyXG4gKiAtIHtpbnN0cnVjY2lvbmVzfTogSW5zdHJ1Y2Npb25lcyBhZGljaW9uYWxlcyAob3BjaW9uYWwpXG4gKi9cbmV4cG9ydCBjb25zdCBQUk9NUFRfRkxBU0hDQVJEUyA9IGBcbkVyZXMgXCJNZW50b3IgT3Bvc2l0b3IgQUlcIiwgdW4gcHJlcGFyYWRvciBkZSBvcG9zaWNpb25lcyB2aXJ0dWFsIGFsdGFtZW50ZSBjdWFsaWZpY2Fkby4gVHUgdGFyZWEgZXMgY3JlYXIgdW4gY29uanVudG8gZGUgZmxhc2hjYXJkcyAodGFyamV0YXMgZGUgZXN0dWRpbykgYmFzYWRhcyBlbiBlbCBjb250ZW5pZG8gcHJvcG9yY2lvbmFkby4gRXN0YXMgZmxhc2hjYXJkcyBzZXLDoW4gdXRpbGl6YWRhcyBwb3IgdW4gZXN0dWRpYW50ZSBwYXJhIHJlcGFzYXIgY29uY2VwdG9zIGNsYXZlLlxuXG5DT05URVhUTyBERUwgVEVNQVJJTyAoSW5mb3JtYWNpw7NuIGJhc2UgcGFyYSB0dXMgZmxhc2hjYXJkcyk6XG57ZG9jdW1lbnRvc31cblxuUEVUSUNJw5NOIERFTCBVU1VBUklPOlxuR2VuZXJhIHtjYW50aWRhZH0gZmxhc2hjYXJkcyBkZSBhbHRhIGNhbGlkYWQuXG57aW5zdHJ1Y2Npb25lc31cblxuSU5TVFJVQ0NJT05FUyBQQVJBIENSRUFSIEZMQVNIQ0FSRFM6XG5cbjEuIEdlbmVyYSBlbnRyZSA1IHkgMTUgZmxhc2hjYXJkcyBkZSBhbHRhIGNhbGlkYWQgYmFzYWRhcyDDmk5JQ0FNRU5URSBlbiBsYSBpbmZvcm1hY2nDs24gcHJvcG9yY2lvbmFkYSBlbiBlbCBDT05URVhUTyBERUwgVEVNQVJJTy5cbjIuIENhZGEgZmxhc2hjYXJkIGRlYmUgdGVuZXI6XG4gICAtIFVuYSBwcmVndW50YSBjbGFyYSB5IGNvbmNpc2EgZW4gZWwgYW52ZXJzb1xuICAgLSBVbmEgcmVzcHVlc3RhIGNvbXBsZXRhIHBlcm8gY29uY2lzYSBlbiBlbCByZXZlcnNvXG4zLiBMYXMgcHJlZ3VudGFzIGRlYmVuIHNlciB2YXJpYWRhcyBlIGluY2x1aXI6XG4gICAtIERlZmluaWNpb25lcyBkZSBjb25jZXB0b3MgY2xhdmVcbiAgIC0gUmVsYWNpb25lcyBlbnRyZSBjb25jZXB0b3NcbiAgIC0gQXBsaWNhY2lvbmVzIHByw6FjdGljYXNcbiAgIC0gQ2xhc2lmaWNhY2lvbmVzIG8gY2F0ZWdvcsOtYXNcbjQuIExhcyByZXNwdWVzdGFzIGRlYmVuOlxuICAgLSBTZXIgcHJlY2lzYXMgeSBiYXNhZGFzIGVzdHJpY3RhbWVudGUgZW4gZWwgY29udGVuaWRvIGRlbCBDT05URVhUT1xuICAgLSBJbmNsdWlyIGxhIGluZm9ybWFjacOzbiBlc2VuY2lhbCBzaW4gc2VyIGV4Y2VzaXZhbWVudGUgbGFyZ2FzXG4gICAtIEVzdGFyIHJlZGFjdGFkYXMgZGUgZm9ybWEgY2xhcmEgeSBkaWTDoWN0aWNhXG41LiBOTyBpbnZlbnRlcyBpbmZvcm1hY2nDs24gcXVlIG5vIGVzdMOpIGVuIGVsIENPTlRFWFRPLlxuNi4gUmVzcG9uZGUgU0lFTVBSRSBlbiBlc3Bhw7FvbC5cblxuRk9STUFUTyBERSBSRVNQVUVTVEE6XG5EZWJlcyBwcm9wb3JjaW9uYXIgdHUgcmVzcHVlc3RhIGVuIGZvcm1hdG8gSlNPTiwgY29uIHVuIGFycmF5IGRlIG9iamV0b3MgZG9uZGUgY2FkYSBvYmpldG8gcmVwcmVzZW50YSB1bmEgZmxhc2hjYXJkIGNvbiBsYXMgcHJvcGllZGFkZXMgXCJwcmVndW50YVwiIHkgXCJyZXNwdWVzdGFcIi4gRWplbXBsbzpcblxuW1xuICB7XG4gICAgXCJwcmVndW50YVwiOiBcIsK/UXXDqSBlcyBYIGNvbmNlcHRvP1wiLFxuICAgIFwicmVzcHVlc3RhXCI6IFwiWCBjb25jZXB0byBlcy4uLlwiXG4gIH0sXG4gIHtcbiAgICBcInByZWd1bnRhXCI6IFwiRW51bWVyYSBsYXMgY2FyYWN0ZXLDrXN0aWNhcyBwcmluY2lwYWxlcyBkZSBZXCIsXG4gICAgXCJyZXNwdWVzdGFcIjogXCJMYXMgY2FyYWN0ZXLDrXN0aWNhcyBwcmluY2lwYWxlcyBkZSBZIHNvbjogMSkuLi4sIDIpLi4uLCAzKS4uLlwiXG4gIH1cbl1cblxuSU1QT1JUQU5URTogVHUgcmVzcHVlc3RhIGRlYmUgY29udGVuZXIgw5pOSUNBTUVOVEUgZWwgYXJyYXkgSlNPTiwgc2luIHRleHRvIGFkaWNpb25hbCBhbnRlcyBvIGRlc3B1w6lzLiBObyBpbmNsdXlhcyBtYXJjYWRvcmVzIGRlIGPDs2RpZ28gbmkgbGEgcGFsYWJyYSBqc29uIGFudGVzIGRlbCBhcnJheS5cbmA7XG5cbi8qKlxuICogUHJvbXB0IHBhcmEgbGEgZ2VuZXJhY2nDs24gZGUgbWFwYXMgbWVudGFsZXNcbiAqXG4gKiBWYXJpYWJsZXMgZGlzcG9uaWJsZXM6XG4gKiAtIHtkb2N1bWVudG9zfTogQ29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3NcbiAqIC0ge2luc3RydWNjaW9uZXN9OiBJbnN0cnVjY2lvbmVzIGFkaWNpb25hbGVzIChvcGNpb25hbClcbiAqL1xuZXhwb3J0IGNvbnN0IFBST01QVF9NQVBBU19NRU5UQUxFUyA9IGBcbkVyZXMgXCJNZW50b3IgT3Bvc2l0b3IgQUlcIiwgdW4gcHJlcGFyYWRvciBkZSBvcG9zaWNpb25lcyB2aXJ0dWFsIGFsdGFtZW50ZSBjdWFsaWZpY2Fkby4gVHUgdGFyZWEgZXMgY3JlYXIgdW4gbWFwYSBtZW50YWwgYmFzYWRvIGVuIGVsIGNvbnRlbmlkbyBwcm9wb3JjaW9uYWRvLiBFc3RlIG1hcGEgbWVudGFsIHNlcsOhIHV0aWxpemFkbyBwb3IgdW4gZXN0dWRpYW50ZSBwYXJhIHZpc3VhbGl6YXIgbGEgZXN0cnVjdHVyYSB5IGxhcyByZWxhY2lvbmVzIGVudHJlIGxvcyBjb25jZXB0b3MuXG5cbkNPTlRFWFRPIERFTCBURU1BUklPIChJbmZvcm1hY2nDs24gYmFzZSBwYXJhIHR1IG1hcGEgbWVudGFsKTpcbntkb2N1bWVudG9zfVxuXG5QRVRJQ0nDk04gREVMIFVTVUFSSU8gKFRlbWEgcHJpbmNpcGFsIHkgZXN0cnVjdHVyYSBkZXNlYWRhIGRlbCBtYXBhIG1lbnRhbCk6XG5HZW5lcmEgdW4gbWFwYSBtZW50YWwgc29icmUgZWwgdGVtYSBwcm9wb3JjaW9uYWRvLlxue2luc3RydWNjaW9uZXN9XG5cbklOU1RSVUNDSU9ORVMgRVhUUkVNQURBTUVOVEUgREVUQUxMQURBUyBQQVJBIEVMIEPDk0RJR08gRDMuSlM6XG5cbioqQS4gRVNUUlVDVFVSQSBERUwgQVJDSElWTyBZIENPTkZJR1VSQUNJw5NOIELDgVNJQ0E6KipcbjEuICAqKkhUTUwgQ29tcGxldG86KiogR2VuZXJhIHVuIHNvbG8gYXJjaGl2byBcXGA8IURPQ1RZUEUgaHRtbD4uLi48L2h0bWw+XFxgLlxuMi4gICoqQ1NTIEludGVncmFkbzoqKiBUb2RvIGVsIENTUyBkZWJlIGVzdGFyIGRlbnRybyBkZSBldGlxdWV0YXMgXFxgPHN0eWxlPlxcYCBlbiBlbCBcXGA8aGVhZD5cXGAuXG4zLiAgKipKYXZhU2NyaXB0IEludGVncmFkbzoqKiBUb2RvIGVsIEphdmFTY3JpcHQgZGViZSBlc3RhciBkZW50cm8gZGUgdW5hIGV0aXF1ZXRhIFxcYDxzY3JpcHQ+XFxgIGFudGVzIGRlIGNlcnJhciBcXGA8L2JvZHk+XFxgLlxuNC4gICoqRDMuanMgQ0ROOioqIENhcmdhIEQzLmpzIHY3IChvIGxhIG3DoXMgcmVjaWVudGUgdjcueCkgZGVzZGUgc3UgQ0ROIG9maWNpYWw6IFxcYGh0dHBzOi8vZDNqcy5vcmcvZDMudjcubWluLmpzXFxgLlxuNS4gICoqU1ZHIHkgQm9keToqKlxuICAgICogICBcXGBib2R5IHsgbWFyZ2luOiAwOyBvdmVyZmxvdzogaGlkZGVuOyBmb250LWZhbWlseTogc2Fucy1zZXJpZjsgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNTsgfVxcYC5cbiAgICAqICAgRWwgXFxgPHN2Zz5cXGAgZGViZSBvY3VwYXIgdG9kYSBsYSB2ZW50YW5hOiBcXGB3aWR0aDogMTAwdnc7IGhlaWdodDogMTAwdmg7XFxgLlxuICAgICogICBBw7FhZGUgdW4gZ3J1cG8gcHJpbmNpcGFsIFxcYDxnIGNsYXNzPVwibWFpbi1ncm91cFwiPlxcYCBkZW50cm8gZGVsIFNWRyBwYXJhIGFwbGljYXIgdHJhbnNmb3JtYWNpb25lcyBkZSB6b29tL3Bhbi5cbiAgICAqICAgKipOVUVWTzoqKiBEZWZpbmUgdW5hIGR1cmFjacOzbiBwYXJhIGxhcyB0cmFuc2ljaW9uZXM6IFxcYGNvbnN0IGR1cmF0aW9uID0gNzUwO1xcYC5cblxuKipCLiBFU1RSVUNUVVJBIERFIERBVE9TIFBBUkEgRDMuSlM6KipcbjEuICAqKkplcmFycXXDrWEgSlNPTjoqKiBFeHRyYWUgbG9zIGNvbmNlcHRvcyBkZWwgQ09OVEVYVE8geSBvcmdhbsOtemFsb3MgZW4gdW5hIGVzdHJ1Y3R1cmEgamVyw6FycXVpY2EgSlNPTi5cbjIuICAqKlByb3BpZWRhZGVzIGRlbCBOb2RvIGRlIERhdG9zOioqIENhZGEgb2JqZXRvIGVuIHR1IGVzdHJ1Y3R1cmEgZGUgZGF0b3MgREVCRSB0ZW5lcjpcbiAgICAqICAgXFxgbmFtZVxcYDogKHN0cmluZykgRWwgdGV4dG8gYSBtb3N0cmFyIGVuIGVsIG5vZG8uXG4gICAgKiAgIFxcYGlkXFxgOiAoc3RyaW5nKSBVbiBpZGVudGlmaWNhZG9yIMOaTklDTyB5IEVTVEFCTEUgcGFyYSBlc3RlIG5vZG8gKGUuZy4sIFwiY29uY2VwdG8tcmFpelwiLCBcImhpam8xLWNvbmNlcHRvLXJhaXpcIikuXG4gICAgKiAgIFxcYGNoaWxkcmVuXFxgOiAoYXJyYXksIG9wY2lvbmFsKSBVbiBhcnJheSBkZSBvYmpldG9zIG5vZG8gaGlqb3MuXG4gICAgKiAgICoqTlVFVk86KiogXFxgX2NoaWxkcmVuXFxgOiAoYXJyYXksIG9wY2lvbmFsLCBpbmljaWFsbWVudGUgbnVsbCBvIHVuZGVmaW5lZCkgU2UgdXNhcsOhIHBhcmEgZ3VhcmRhciBsb3MgaGlqb3MgY3VhbmRvIHVuIG5vZG8gZXN0w6kgY29sYXBzYWRvLlxuMy4gICoqSmVyYXJxdcOtYSBEMzoqKiBVc2EgXFxgbGV0IHJvb3QgPSBkMy5oaWVyYXJjaHkoZGF0b3NKU09OKTtcXGAuXG40LiAgKipOVUVWTzogQ29sYXBzYXIgTm9kb3MgSW5pY2lhbG1lbnRlIChPcGNpb25hbCwgcGVybyBidWVubyBwYXJhIGVsIHJlbmRpbWllbnRvIHNpIGVsIMOhcmJvbCBlcyBncmFuZGUpOioqXG4gICAgKiAgIERlZmluZSB1bmEgZnVuY2nDs24gXFxgY29sbGFwc2Uobm9kZSlcXGAgcXVlIG11ZXZlIFxcYG5vZGUuY2hpbGRyZW5cXGAgYSBcXGBub2RlLl9jaGlsZHJlblxcYCBwYXJhIHRvZG9zIGxvcyBkZXNjZW5kaWVudGVzIGV4Y2VwdG8gZWwgbm9kbyByYcOteiB5IHN1cyBoaWpvcyBkaXJlY3Rvcy5cbiAgICAqICAgTGxhbWEgYSBcXGByb290LmRlc2NlbmRhbnRzKCkuZm9yRWFjaChjb2xsYXBzZSk7XFxgIGRlc3B1w6lzIGRlIGNyZWFyIGxhIGplcmFycXXDrWEgeSBhbnRlcyBkZWwgcHJpbWVyIHJlbmRlcml6YWRvLCBzaSBxdWllcmVzIHF1ZSBlbCDDoXJib2wgZW1waWVjZSBwYXJjaWFsbWVudGUgY29sYXBzYWRvLiBPIHB1ZWRlcyBkZWphcmxvIHRvZG8gZXhwYW5kaWRvIHkgcXVlIGVsIHVzdWFyaW8gY29sYXBzZS5cbiAgICAqICAgKipBbHRlcm5hdGl2YSBtw6FzIHNpbXBsZSBwYXJhIGluaWNpbzoqKiBDb2xhcHNhIHRvZG9zIGxvcyBub2RvcyBhIHBhcnRpciBkZSBjaWVydGEgcHJvZnVuZGlkYWQgKGVqLiBwcm9mdW5kaWRhZCA+IDEpLlxuICAgICAgXFxgcm9vdC5lYWNoKGQgPT4geyBpZiAoZC5kZXB0aCA+IDEpIHsgaWYgKGQuY2hpbGRyZW4pIHsgZC5fY2hpbGRyZW4gPSBkLmNoaWxkcmVuOyBkLmNoaWxkcmVuID0gbnVsbDsgfSB9IH0pO1xcYFxuXG4qKkMuIExBWU9VVCBERUwgw4FSQk9MIChEMy5KUyBUUkVFKToqKlxuMS4gICoqVGlwbyBkZSBMYXlvdXQ6KiogVXNhIFxcYGQzLnRyZWUoKVxcYC5cbjIuICAqKkVzcGFjaWFkbyBkZSBOb2RvcyAoXFxgbm9kZVNpemVcXGApOioqXG4gICAgKiAgIFxcYGNvbnN0IG5vZGVWZXJ0aWNhbFNlcGFyYXRpb24gPSA4MDtcXGAuXG4gICAgKiAgIFxcYGNvbnN0IG5vZGVIb3Jpem9udGFsU2VwYXJhdGlvbiA9IDI1MDtcXGAuXG4gICAgKiAgIFxcYGNvbnN0IHRyZWVMYXlvdXQgPSBkMy50cmVlKCkubm9kZVNpemUoW25vZGVWZXJ0aWNhbFNlcGFyYXRpb24sIG5vZGVIb3Jpem9udGFsU2VwYXJhdGlvbl0pO1xcYC5cbjMuICAqKlBvc2ljacOzbiBJbmljaWFsOioqIEd1YXJkYSBsYSBwb3NpY2nDs24gaW5pY2lhbCBkZSBsYSByYcOteiBjb24gdmFsaWRhY2nDs246XG4gICAgXFxgY29uc3Qgdmlld3BvcnRIZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQgfHwgNjAwO1xuICAgICBjb25zdCB2aWV3cG9ydFdpZHRoID0gd2luZG93LmlubmVyV2lkdGggfHwgODAwO1xuICAgICByb290LngwID0gaXNOYU4odmlld3BvcnRIZWlnaHQgLyAyKSA/IDMwMCA6IHZpZXdwb3J0SGVpZ2h0IC8gMjtcbiAgICAgcm9vdC55MCA9IDA7XFxgIChBanVzdGEgeTAgc2kgbGEgcmHDrXogbm8gZW1waWV6YSBlbiBlbCBib3JkZSkuXG5cbioqRC4gRlVOQ0nDk04gXFxgdXBkYXRlKHNvdXJjZU5vZGUpXFxgIChWSVRBTCBQQVJBIElOVEVSQUNUSVZJREFEKToqKlxuICAgRXN0YSBmdW5jacOzbiBzZXLDoSBsYSByZXNwb25zYWJsZSBkZSByZW5kZXJpemFyL2FjdHVhbGl6YXIgZWwgw6FyYm9sIGNhZGEgdmV6IHF1ZSBzZSBleHBhbmRhL2NvbGFwc2UgdW4gbm9kby5cbiAgIFxcYHNvdXJjZU5vZGVcXGAgZXMgZWwgbm9kbyBxdWUgZnVlIGNsaWNrZWFkby5cblxuMS4gICoqQ2FsY3VsYXIgTnVldm8gTGF5b3V0OioqXG4gICAgKiAgIFxcYGNvbnN0IHRyZWVEYXRhID0gdHJlZUxheW91dChyb290KTtcXGAuXG4gICAgKiAgIFxcYGNvbnN0IG5vZGVzID0gdHJlZURhdGEuZGVzY2VuZGFudHMoKTtcXGAuXG4gICAgKiAgIFxcYGNvbnN0IGxpbmtzID0gdHJlZURhdGEubGlua3MoKTtcXGAuXG4gICAgKiAgICoqT3JpZW50YWNpw7NuIChBanVzdGFyIENvb3JkZW5hZGFzKToqKiBBc2Vnw7pyYXRlIGRlIHF1ZSBkZXNwdcOpcyBkZWwgbGF5b3V0LCBsb3Mgbm9kb3Mgc2UgcG9zaWNpb25lbiBob3Jpem9udGFsbWVudGUuIFxcYG5vZGVzLmZvckVhY2goZCA9PiB7IGQueSA9IGQuZGVwdGggKiBub2RlSG9yaXpvbnRhbFNlcGFyYXRpb247IH0pO1xcYCAoU2kgXFxgbm9kZVNpemVcXGAgbm8gbG8gaGFjZSBkaXJlY3RhbWVudGUsIG8gc2kgcXVpZXJlcyBjb250cm9sYXIgbGEgc2VwYXJhY2nDs24gZGUgbml2ZWxlcyBtYW51YWxtZW50ZSkuXG4gICAgKiAgICoqVkFMSURBQ0nDk04gQ1LDjVRJQ0E6KiogQXNlZ8O6cmF0ZSBkZSBxdWUgdG9kYXMgbGFzIGNvb3JkZW5hZGFzIHNlYW4gbsO6bWVyb3MgdsOhbGlkb3M6XG4gICAgICAgIFxcYG5vZGVzLmZvckVhY2goZCA9PiB7XG4gICAgICAgICAgZC54ID0gaXNOYU4oZC54KSA/IDAgOiBkLng7XG4gICAgICAgICAgZC55ID0gaXNOYU4oZC55KSA/IGQuZGVwdGggKiBub2RlSG9yaXpvbnRhbFNlcGFyYXRpb24gOiBkLnk7XG4gICAgICAgICAgZC54MCA9IGQueDAgfHwgZC54O1xuICAgICAgICAgIGQueTAgPSBkLnkwIHx8IGQueTtcbiAgICAgICAgfSk7XFxgXG5cbjIuICAqKk5PRE9TOioqXG4gICAgKiAgIFNlbGVjY2nDs246IFxcYGNvbnN0IG5vZGUgPSBnLnNlbGVjdEFsbChcImcubm9kZVwiKS5kYXRhKG5vZGVzLCBkID0+IGQuZGF0YS5pZCk7XFxgLlxuICAgICogICAqKk5vZG9zIEVudHJhbnRlcyAoXFxgbm9kZUVudGVyXFxgKToqKlxuICAgICAgICAqICAgQcOxYWRlIHVuIGdydXBvIFxcYDxnIGNsYXNzPVwibm9kZVwiPlxcYC5cbiAgICAgICAgKiAgICoqVkFMSURBQ0nDk04gREUgUE9TSUNJw5NOIElOSUNJQUw6KiogXFxgY29uc3Qgc291cmNlWCA9IHNvdXJjZU5vZGUueDAgfHwgMDsgY29uc3Qgc291cmNlWSA9IHNvdXJjZU5vZGUueTAgfHwgMDtcXGBcbiAgICAgICAgKiAgIFRyYW5zZm9ybWFjacOzbiBpbmljaWFsIGVuIGxhIHBvc2ljacOzbiBkZWwgbm9kbyBwYWRyZSAoc291cmNlTm9kZSk6IFxcYG5vZGVFbnRlci5hcHBlbmQoXCJnXCIpLmF0dHIoXCJjbGFzc1wiLCBcIm5vZGVcIikuYXR0cihcInRyYW5zZm9ybVwiLCBcXGB0cmFuc2xhdGUoXFwke3NvdXJjZVl9LFxcJHtzb3VyY2VYfSlcXGApLm9uKFwiY2xpY2tcIiwgaGFuZGxlQ2xpY2spO1xcYFxuICAgICAgICAqICAgKipDw6FsY3VsbyBkZSBEaW1lbnNpb25lcyBkZWwgUmVjdMOhbmd1bG8gKFZJVEFMLCBzZSBoYWNlIGFxdcOtIHBhcmEgY2FkYSBub2RvIHF1ZSBlbnRyYSk6KipcbiAgICAgICAgICAgICogICBBw7FhZGUgXFxgPHRleHQ+XFxgOiBcXGB0ZXh0LWFuY2hvcj1cIm1pZGRsZVwiXFxgLCBcXGBkb21pbmFudC1iYXNlbGluZT1cImNlbnRyYWxcIlxcYCwgXFxgZm9udC1zaXplOiAxMHB4O1xcYCwgXFxgZmlsbDogIzMzMztcXGAuIENvbnRlbmlkbzogXFxgZC5kYXRhLm5hbWVcXGAuXG4gICAgICAgICAgICAqICAgKipDw4FMQ1VMTyBTRUdVUk8gREUgRElNRU5TSU9ORVM6KipcbiAgICAgICAgICAgICAgICBcXGBub2RlRW50ZXIuZWFjaChmdW5jdGlvbihkKSB7XG4gICAgICAgICAgICAgICAgICBjb25zdCB0ZXh0RWxlbWVudCA9IGQzLnNlbGVjdCh0aGlzKS5zZWxlY3QoXCJ0ZXh0XCIpO1xuICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGV4dEJCb3ggPSB0ZXh0RWxlbWVudC5ub2RlKCkuZ2V0QkJveCgpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBob3Jpem9udGFsUGFkZGluZyA9IDEwO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB2ZXJ0aWNhbFBhZGRpbmcgPSA2O1xuICAgICAgICAgICAgICAgICAgICBkLnJlY3RXaWR0aCA9IE1hdGgubWF4KHRleHRCQm94LndpZHRoICsgMiAqIGhvcml6b250YWxQYWRkaW5nLCA0MCk7XG4gICAgICAgICAgICAgICAgICAgIGQucmVjdEhlaWdodCA9IE1hdGgubWF4KHRleHRCQm94LmhlaWdodCArIDIgKiB2ZXJ0aWNhbFBhZGRpbmcsIDIwKTtcbiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gRmFsbGJhY2sgc2kgZ2V0QkJveCgpIGZhbGxhXG4gICAgICAgICAgICAgICAgICAgIGQucmVjdFdpZHRoID0gTWF0aC5tYXgoZC5kYXRhLm5hbWUubGVuZ3RoICogOCArIDIwLCA0MCk7XG4gICAgICAgICAgICAgICAgICAgIGQucmVjdEhlaWdodCA9IDIwO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xcYFxuICAgICAgICAqICAgQcOxYWRlIFxcYDxyZWN0PlxcYDpcbiAgICAgICAgICAgICogICBBbmNobyB5IGFsdG8gY29uIFxcYGQucmVjdFdpZHRoXFxgLCBcXGBkLnJlY3RIZWlnaHRcXGAuXG4gICAgICAgICAgICAqICAgUG9zaWNpw7NuIFxcYHggPSAtZC5yZWN0V2lkdGggLyAyXFxgLCBcXGB5ID0gLWQucmVjdEhlaWdodCAvIDJcXGAuXG4gICAgICAgICAgICAqICAgRXN0aWxvOiBcXGByeD1cIjNcIlxcYCwgXFxgcnk9XCIzXCJcXGAsIFxcYHN0cm9rZS13aWR0aDogMXB4O1xcYC5cbiAgICAgICAgICAgICogICBDb2xvcmVzIChwdWVkZXMgdXNhciBDU1MpOiBcXGBmaWxsOiBkID0+IGQuX2NoaWxkcmVuID8gXCIjYWVjN2U4XCIgOiBcIiNmZmZcIjsgc3Ryb2tlOiBcIiM3NzdcIjtcXGAgKEF6dWwgc2kgY29sYXBzYWJsZSwgYmxhbmNvIHNpIGhvamEpLlxuICAgICAgICAqICAgUmVwb3NpY2lvbmEgZWwgdGV4dG8gKHNpIGVzIG5lY2VzYXJpbywgYXVucXVlIFxcYGRvbWluYW50LWJhc2VsaW5lPVwiY2VudHJhbFwiXFxgIGRlYmVyw61hIGJhc3RhcikuXG4gICAgKiAgICoqTm9kb3MgQWN0dWFsaXphZG9zIChcXGBub2RlVXBkYXRlXFxgKToqKlxuICAgICAgICAqICAgKipWQUxJREFDScOTTiBERSBDT09SREVOQURBUzoqKiBcXGBjb25zdCB2YWxpZFggPSBpc05hTihkLngpID8gMCA6IGQueDsgY29uc3QgdmFsaWRZID0gaXNOYU4oZC55KSA/IDAgOiBkLnk7XFxgXG4gICAgICAgICogICBUcmFuc2ljacOzbiBhIGxhIG51ZXZhIHBvc2ljacOzbjogXFxgbm9kZS5tZXJnZShub2RlRW50ZXIpLnRyYW5zaXRpb24oKS5kdXJhdGlvbihkdXJhdGlvbikuYXR0cihcInRyYW5zZm9ybVwiLCBkID0+IFxcYHRyYW5zbGF0ZShcXCR7aXNOYU4oZC55KSA/IDAgOiBkLnl9LFxcJHtpc05hTihkLngpID8gMCA6IGQueH0pXFxgKTtcXGAuXG4gICAgICAgICogICBBY3R1YWxpemEgZWwgY29sb3IgZGVsIHJlY3TDoW5ndWxvIHNpIGNhbWJpYSBlbCBlc3RhZG8gY29sYXBzYWJsZTogXFxgbm9kZS5tZXJnZShub2RlRW50ZXIpLnNlbGVjdChcInJlY3RcIikuc3R5bGUoXCJmaWxsXCIsIGQgPT4gZC5fY2hpbGRyZW4gPyBcIiNhZWM3ZThcIiA6IFwiI2ZmZlwiKTtcXGAuXG4gICAgKiAgICoqTm9kb3MgU2FsaWVudGVzIChcXGBub2RlRXhpdFxcYCk6KipcbiAgICAgICAgKiAgICoqVkFMSURBQ0nDk04gREUgUE9TSUNJw5NOIEZJTkFMOioqIFxcYGNvbnN0IGZpbmFsWCA9IGlzTmFOKHNvdXJjZU5vZGUueCkgPyAwIDogc291cmNlTm9kZS54OyBjb25zdCBmaW5hbFkgPSBpc05hTihzb3VyY2VOb2RlLnkpID8gMCA6IHNvdXJjZU5vZGUueTtcXGBcbiAgICAgICAgKiAgIFRyYW5zaWNpw7NuIGEgbGEgcG9zaWNpw7NuIGRlbCBub2RvIHBhZHJlOiBcXGBub2RlRXhpdC50cmFuc2l0aW9uKCkuZHVyYXRpb24oZHVyYXRpb24pLmF0dHIoXCJ0cmFuc2Zvcm1cIiwgXFxgdHJhbnNsYXRlKFxcJHtmaW5hbFl9LFxcJHtmaW5hbFh9KVxcYCkucmVtb3ZlKCk7XFxgLlxuICAgICAgICAqICAgUmVkdWNlIGxhIG9wYWNpZGFkIGRlbCByZWN0w6FuZ3VsbyB5IHRleHRvIGEgMC5cblxuMy4gICoqRU5MQUNFUzoqKlxuICAgICogICBTZWxlY2Npw7NuOiBcXGBjb25zdCBsaW5rID0gZy5zZWxlY3RBbGwoXCJwYXRoLmxpbmtcIikuZGF0YShsaW5rcywgZCA9PiBkLnRhcmdldC5kYXRhLmlkKTtcXGAuXG4gICAgKiAgICoqRW5sYWNlcyBFbnRyYW50ZXMgKFxcYGxpbmtFbnRlclxcYCk6KipcbiAgICAgICAgKiAgIEHDsWFkZSBcXGA8cGF0aCBjbGFzcz1cImxpbmtcIj5cXGAuXG4gICAgICAgICogICAqKlZBTElEQUNJw5NOIERFIFBPU0lDScOTTiBJTklDSUFMOioqXG4gICAgICAgICAgICBcXGBjb25zdCBzb3VyY2VJbml0aWFsWCA9IGlzTmFOKHNvdXJjZU5vZGUueDApID8gMCA6IHNvdXJjZU5vZGUueDA7XG4gICAgICAgICAgICAgY29uc3Qgc291cmNlSW5pdGlhbFkgPSBpc05hTihzb3VyY2VOb2RlLnkwKSA/IDAgOiBzb3VyY2VOb2RlLnkwO1xuICAgICAgICAgICAgIGNvbnN0IHNvdXJjZUluaXRpYWxXaWR0aCA9IGlzTmFOKHNvdXJjZU5vZGUucmVjdFdpZHRoKSA/IDIwIDogKHNvdXJjZU5vZGUucmVjdFdpZHRoIHx8IDIwKTtcXGBcbiAgICAgICAgKiAgIFBvc2ljacOzbiBpbmljaWFsIGRlc2RlIGVsIHBhZHJlOiBcXGBsaW5rRW50ZXIuaW5zZXJ0KFwicGF0aFwiLCBcImdcIikuYXR0cihcImNsYXNzXCIsIFwibGlua1wiKS5hdHRyKFwiZFwiLCBkID0+IHsgY29uc3QgbyA9IHt4OiBzb3VyY2VJbml0aWFsWCwgeTogc291cmNlSW5pdGlhbFksIHJlY3RXaWR0aDogc291cmNlSW5pdGlhbFdpZHRoIH07IHJldHVybiBkaWFnb25hbCh7c291cmNlOiBvLCB0YXJnZXQ6IG99KTsgfSkuc3R5bGUoXCJmaWxsXCIsIFwibm9uZVwiKS5zdHlsZShcInN0cm9rZVwiLCBcIiNjY2NcIikuc3R5bGUoXCJzdHJva2Utd2lkdGhcIiwgXCIxLjVweFwiKTtcXGBcbiAgICAqICAgKipFbmxhY2VzIEFjdHVhbGl6YWRvcyAoXFxgbGlua1VwZGF0ZVxcYCk6KipcbiAgICAgICAgKiAgIFRyYW5zaWNpw7NuIGEgbGEgbnVldmEgcG9zaWNpw7NuOiBcXGBsaW5rLm1lcmdlKGxpbmtFbnRlcikudHJhbnNpdGlvbigpLmR1cmF0aW9uKGR1cmF0aW9uKS5hdHRyKFwiZFwiLCBkaWFnb25hbCk7XFxgLlxuICAgICogICAqKkVubGFjZXMgU2FsaWVudGVzIChcXGBsaW5rRXhpdFxcYCk6KipcbiAgICAgICAgKiAgICoqVkFMSURBQ0nDk04gREUgUE9TSUNJw5NOIEZJTkFMOioqXG4gICAgICAgICAgICBcXGBjb25zdCBzb3VyY2VGaW5hbFggPSBpc05hTihzb3VyY2VOb2RlLngpID8gMCA6IHNvdXJjZU5vZGUueDtcbiAgICAgICAgICAgICBjb25zdCBzb3VyY2VGaW5hbFkgPSBpc05hTihzb3VyY2VOb2RlLnkpID8gMCA6IHNvdXJjZU5vZGUueTtcbiAgICAgICAgICAgICBjb25zdCBzb3VyY2VGaW5hbFdpZHRoID0gaXNOYU4oc291cmNlTm9kZS5yZWN0V2lkdGgpID8gMjAgOiAoc291cmNlTm9kZS5yZWN0V2lkdGggfHwgMjApO1xcYFxuICAgICAgICAqICAgVHJhbnNpY2nDs24gYSBsYSBwb3NpY2nDs24gZGVsIHBhZHJlIHkgcmVtb3ZlOiBcXGBsaW5rRXhpdC50cmFuc2l0aW9uKCkuZHVyYXRpb24oZHVyYXRpb24pLmF0dHIoXCJkXCIsIGQgPT4geyBjb25zdCBvID0ge3g6IHNvdXJjZUZpbmFsWCwgeTogc291cmNlRmluYWxZLCByZWN0V2lkdGg6IHNvdXJjZUZpbmFsV2lkdGggfTsgcmV0dXJuIGRpYWdvbmFsKHtzb3VyY2U6IG8sIHRhcmdldDogb30pOyB9KS5yZW1vdmUoKTtcXGAuXG5cbjQuICAqKkd1YXJkYXIgUG9zaWNpb25lcyBBbnRpZ3VhczoqKlxuICAgICogICBBbCBmaW5hbCBkZSBcXGB1cGRhdGVcXGA6IFxcYG5vZGVzLmZvckVhY2goZCA9PiB7IGQueDAgPSBkLng7IGQueTAgPSBkLnk7IH0pO1xcYC5cblxuKipFLiBGVU5DScOTTiBcXGBkaWFnb25hbChsaW5rT2JqZWN0KVxcYCAoUEFSQSBESUJVSkFSIEVOTEFDRVMgQSBCT1JERVMgREUgUkVDVMOBTkdVTE9TKToqKlxuICAgRGViZSBnZW5lcmFyIHVuIHBhdGggc3RyaW5nIHBhcmEgZWwgYXRyaWJ1dG8gXFxgZFxcYCBkZWwgcGF0aC5cbiAgIFxcYFxcYFxcYGphdmFzY3JpcHRcbiAgIGZ1bmN0aW9uIGRpYWdvbmFsKHsgc291cmNlLCB0YXJnZXQgfSkge1xuICAgICAvLyBzb3VyY2UgeSB0YXJnZXQgc29uIG5vZG9zIGNvbiBwcm9waWVkYWRlcyB4LCB5LCByZWN0V2lkdGhcbiAgICAgLy8gVkFMSURBQ0nDk04gQ1LDjVRJQ0E6IEFzZWd1cmFyIHF1ZSB0b2RvcyBsb3MgdmFsb3JlcyBzZWFuIG7Dum1lcm9zIHbDoWxpZG9zXG4gICAgIGNvbnN0IHNvdXJjZVggPSBpc05hTihzb3VyY2UueCkgPyAwIDogc291cmNlLng7XG4gICAgIGNvbnN0IHNvdXJjZVkgPSBpc05hTihzb3VyY2UueSkgPyAwIDogc291cmNlLnk7XG4gICAgIGNvbnN0IHRhcmdldFggPSBpc05hTih0YXJnZXQueCkgPyAwIDogdGFyZ2V0Lng7XG4gICAgIGNvbnN0IHRhcmdldFkgPSBpc05hTih0YXJnZXQueSkgPyAwIDogdGFyZ2V0Lnk7XG4gICAgIGNvbnN0IHNvdXJjZVdpZHRoID0gaXNOYU4oc291cmNlLnJlY3RXaWR0aCkgPyAyMCA6IChzb3VyY2UucmVjdFdpZHRoIHx8IDIwKTtcbiAgICAgY29uc3QgdGFyZ2V0V2lkdGggPSBpc05hTih0YXJnZXQucmVjdFdpZHRoKSA/IDIwIDogKHRhcmdldC5yZWN0V2lkdGggfHwgMjApO1xuXG4gICAgIGNvbnN0IHN4ID0gc291cmNlWSArIHNvdXJjZVdpZHRoIC8gMjtcbiAgICAgY29uc3Qgc3kgPSBzb3VyY2VYO1xuICAgICBjb25zdCB0eCA9IHRhcmdldFkgLSB0YXJnZXRXaWR0aCAvIDI7XG4gICAgIGNvbnN0IHR5ID0gdGFyZ2V0WDtcblxuICAgICAvLyBWYWxpZGFyIHF1ZSBsb3MgcHVudG9zIGNhbGN1bGFkb3Mgc2VhbiBuw7ptZXJvcyB2w6FsaWRvc1xuICAgICBjb25zdCB2YWxpZFN4ID0gaXNOYU4oc3gpID8gMCA6IHN4O1xuICAgICBjb25zdCB2YWxpZFN5ID0gaXNOYU4oc3kpID8gMCA6IHN5O1xuICAgICBjb25zdCB2YWxpZFR4ID0gaXNOYU4odHgpID8gMCA6IHR4O1xuICAgICBjb25zdCB2YWxpZFR5ID0gaXNOYU4odHkpID8gMCA6IHR5O1xuXG4gICAgIC8vIFBhdGggY3VydmFkbyBzaW1wbGVcbiAgICAgcmV0dXJuIFxcYE0gXFwke3ZhbGlkU3h9IFxcJHt2YWxpZFN5fVxuICAgICAgICAgICAgIEMgXFwkeyh2YWxpZFN4ICsgdmFsaWRUeCkgLyAyfSBcXCR7dmFsaWRTeX0sXG4gICAgICAgICAgICAgICBcXCR7KHZhbGlkU3ggKyB2YWxpZFR4KSAvIDJ9IFxcJHt2YWxpZFR5fSxcbiAgICAgICAgICAgICAgIFxcJHt2YWxpZFR4fSBcXCR7dmFsaWRUeX1cXGA7XG4gICB9XG4gICBcXGBcXGBcXGBcblxuKipGLiBGVU5DScOTTiBcXGBoYW5kbGVDbGljayhldmVudCwgZClcXGAgKE1BTkVKQURPUiBERSBDTElDIEVOIE5PRE8pOioqXG4gICBcXGBcXGBcXGBqYXZhc2NyaXB0XG4gICBmdW5jdGlvbiBoYW5kbGVDbGljayhldmVudCwgZCkge1xuICAgICBpZiAoZC5jaGlsZHJlbikgeyAvLyBTaSBlc3TDoSBleHBhbmRpZG8sIGNvbGFwc2FyXG4gICAgICAgZC5fY2hpbGRyZW4gPSBkLmNoaWxkcmVuO1xuICAgICAgIGQuY2hpbGRyZW4gPSBudWxsO1xuICAgICB9IGVsc2UgaWYgKGQuX2NoaWxkcmVuKSB7IC8vIFNpIGVzdMOhIGNvbGFwc2FkbyB5IHRpZW5lIGhpam9zIG9jdWx0b3MsIGV4cGFuZGlyXG4gICAgICAgZC5jaGlsZHJlbiA9IGQuX2NoaWxkcmVuO1xuICAgICAgIGQuX2NoaWxkcmVuID0gbnVsbDtcbiAgICAgfVxuICAgICAvLyBTaSBlcyB1biBub2RvIGhvamEgKHNpbiBkLmNoaWxkcmVuIG5pIGQuX2NoaWxkcmVuKSwgbm8gaGFjZXIgbmFkYSBvIHVuYSBhY2Npw7NuIGVzcGVjw61maWNhLlxuICAgICAvLyBQYXJhIGVzdGUgY2Fzbywgc29sbyBleHBhbmRpci9jb2xhcHNhci5cbiAgICAgdXBkYXRlKGQpOyAvLyBMbGFtYSBhIHVwZGF0ZSBjb24gZWwgbm9kbyBjbGlja2VhZG8gY29tbyAnc291cmNlTm9kZSdcbiAgIH1cbiAgIFxcYFxcYFxcYFxuXG4qKkcuIFZJU1VBTElaQUNJw5NOIElOSUNJQUwgWSBaT09NL1BBTjoqKlxuMS4gIExsYW1hIGEgXFxgdXBkYXRlKHJvb3QpO1xcYCBwYXJhIGVsIHByaW1lciByZW5kZXJpemFkby5cbjIuICAqKkPDoWxjdWxvIGRlIEV4dGVuc2lvbmVzIHkgRXNjYWxhIEluaWNpYWwgKEFkYXB0YXIgZGVsIHByb21wdCBhbnRlcmlvcik6KipcbiAgICAqICAgTkVDRVNJVEFTIGNhbGN1bGFyIGxhcyBkaW1lbnNpb25lcyBkZWwgw6FyYm9sIERFU1BVw4lTIGRlIHF1ZSBlbCBsYXlvdXQgaW5pY2lhbCAoXFxgdXBkYXRlKHJvb3QpXFxgKSBoYXlhIGFzaWduYWRvIFxcYHJlY3RXaWR0aFxcYCB5IFxcYHJlY3RIZWlnaHRcXGAgYSBsb3Mgbm9kb3MgdmlzaWJsZXMuXG4gICAgKiAgIE9idMOpbiBtaW5YLCBtYXhYLCBtaW5ZQWN0dWFsLCBtYXhZQWN0dWFsIGRlIGxvcyBub2RvcyBlbiByb290LmRlc2NlbmRhbnRzKCkgcXVlIG5vIGVzdMOpbiBjb2xhcHNhZG9zIChvIGRlIHRvZG9zIHBhcmEgdW4gY8OhbGN1bG8gbcOhcyBzaW1wbGUgcXVlIHB1ZWRlIHNlciBhanVzdGFkbyBwb3IgZWwgem9vbSkuXG4gICAgKiAgIENvbnNpZGVyYSBlbCBcXGByZWN0V2lkdGgvMlxcYCB5IFxcYHJlY3RIZWlnaHQvMlxcYCBwYXJhIGxvcyBib3JkZXMuXG4zLiAgKipUcmFzbGFjacOzbiB5IEVzY2FsYToqKlxuICAgICogICBDYWxjdWxhIFxcYGluaXRpYWxTY2FsZVxcYCwgXFxgaW5pdGlhbFRyYW5zbGF0ZVhcXGAsIFxcYGluaXRpYWxUcmFuc2xhdGVZXFxgIGNvbW8gZW4gZWwgcHJvbXB0IGFudGVyaW9yLCBwZXJvIHVzYW5kbyBlbCBcXGA8ZyBjbGFzcz1cIm1haW4tZ3JvdXBcIj5cXGAgcGFyYSBlbCB6b29tLlxuICAgICogICBcXGBjb25zdCB6b29tID0gZDMuem9vbSgpLnNjYWxlRXh0ZW50KFswLjEsIDNdKS5vbihcInpvb21cIiwgKGV2ZW50KSA9PiBtYWluR3JvdXAuYXR0cihcInRyYW5zZm9ybVwiLCBldmVudC50cmFuc2Zvcm0pKTtcXGBcbiAgICAqICAgXFxgc3ZnLmNhbGwoem9vbSk7XFxgLlxuICAgICogICBcXGBzdmcuY2FsbCh6b29tLnRyYW5zZm9ybSwgZDMuem9vbUlkZW50aXR5LnRyYW5zbGF0ZShpbml0aWFsVHJhbnNsYXRlWCwgaW5pdGlhbFRyYW5zbGF0ZVkpLnNjYWxlKGluaXRpYWxTY2FsZSkpO1xcYC5cblxuKipILiBNQU5FSk8gREUgUkVESU1FTlNJT05BTUlFTlRPIERFIFZFTlRBTkEgKENvbW8gZW4gZWwgcHJvbXB0IGFudGVyaW9yKToqKlxuICAgICogICBSZWFqdXN0YSBlbCBTVkcgeSByZWNhbGN1bGEgbGEgdHJhbnNmb3JtYWNpw7NuIGRlIHpvb20vcGFuIHBhcmEgY2VudHJhci5cblxuKipJLiBFU1RJTE8gQ1NTOioqXG4gICBcXGBcXGBcXGBjc3NcbiAgIC5ub2RlIHRleHQgeyBmb250OiAxMHB4IHNhbnMtc2VyaWY7IHBvaW50ZXItZXZlbnRzOiBub25lOyB9XG4gICAubGluayB7IGZpbGw6IG5vbmU7IHN0cm9rZTogI2NjYzsgc3Ryb2tlLXdpZHRoOiAxLjVweDsgfVxuICAgLm5vZGUgcmVjdCB7IGN1cnNvcjogcG9pbnRlcjsgfVxuICAgLm5vZGUgcmVjdDpob3ZlciB7IHN0cm9rZS1vcGFjaXR5OiAxOyBzdHJva2Utd2lkdGg6IDJweDsgfVxuICAgLyogQ29sb3JlcyBwb3IgcHJvZnVuZGlkYWQgKG9wY2lvbmFsKSAqL1xuICAgLm5vZGUuZGVwdGgtMCByZWN0IHsgZmlsbDogI2QxZTVmMDsgc3Ryb2tlOiAjNjdhOWNmOyB9XG4gICAubm9kZS5kZXB0aC0xIHJlY3QgeyBmaWxsOiAjZmRkYmM3OyBzdHJva2U6ICNlZjhhNjI7IH1cbiAgIC5ub2RlLmRlcHRoLTIgcmVjdCB7IGZpbGw6ICNlMGYzZjg7IHN0cm9rZTogIzkyYzVkZTsgfVxuICAgLm5vZGUuZGVwdGgtMyByZWN0IHsgZmlsbDogI2Y3ZjdmNzsgc3Ryb2tlOiAjYmFiYWJhOyB9XG4gICBcXGBcXGBcXGBcbiAgIEFzZWfDunJhdGUgZGUgYcOxYWRpciBsYSBjbGFzZSBkZSBwcm9mdW5kaWRhZCBhbCBncnVwbyBkZWwgbm9kbzpcbiAgIFxcYG5vZGVFbnRlci5hdHRyKFwiY2xhc3NcIiwgZCA9PiBcIm5vZGUgZGVwdGgtXCIgKyBkLmRlcHRoKVxcYFxuXG4qKkouIFJFVklTScOTTiBGSU5BTCBBTlRFUyBERSBHRU5FUkFSIChQQVJBIExBIElBKToqKlxuKiAgIMK/U2UgdXNhIHVuYSBmdW5jacOzbiBcXGB1cGRhdGUoc291cmNlTm9kZSlcXGAgcGFyYSBtYW5lamFyIHRvZGFzIGxhcyBhY3R1YWxpemFjaW9uZXMgZGVsIERPTT8gU8ONLlxuKiAgIMK/TGEgZnVuY2nDs24gXFxgaGFuZGxlQ2xpY2tcXGAgYWx0ZXJuYSBlbnRyZSBcXGBkLmNoaWxkcmVuXFxgIHkgXFxgZC5fY2hpbGRyZW5cXGAgeSBsdWVnbyBsbGFtYSBhIFxcYHVwZGF0ZShkKVxcYD8gU8ONLlxuKiAgIMK/TG9zIG5vZG9zIHkgZW5sYWNlcyBlbnRyYW50ZXMgYXBhcmVjZW4gZGVzZGUgbGEgcG9zaWNpw7NuIGRlbCBwYWRyZSAoXFxgc291cmNlTm9kZVxcYCk/IFPDjS5cbiogICDCv0xvcyBub2RvcyB5IGVubGFjZXMgc2FsaWVudGVzIHNlIG11ZXZlbiBoYWNpYSBsYSBwb3NpY2nDs24gZGVsIHBhZHJlIGFudGVzIGRlIGVsaW1pbmFyc2U/IFPDjS5cbiogICDCv1NlIHVzYW4gdHJhbnNpY2lvbmVzIEQzIGNvbiB1bmEgXFxgZHVyYXRpb25cXGAgY29uc3RhbnRlPyBTw40uXG4qICAgwr9TZSBhbG1hY2VuYW4geSB1c2FuIFxcYHgwXFxgLCBcXGB5MFxcYCBwYXJhIGxhcyBwb3NpY2lvbmVzIGluaWNpYWxlcy9maW5hbGVzIGRlIGxhcyB0cmFuc2ljaW9uZXM/IFPDjS5cbiogICDCv0xhIGZ1bmNpw7NuIFxcYGRpYWdvbmFsXFxgIGNhbGN1bGEgY29ycmVjdGFtZW50ZSBsb3MgcHVudG9zIGRlIGluaWNpby9maW4gZW4gbG9zIGJvcmRlcyBkZSBsb3MgcmVjdMOhbmd1bG9zPyBTw40uXG4qICAgwr9FbCBjw6FsY3VsbyBkaW7DoW1pY28gZGUgXFxgcmVjdFdpZHRoXFxgIHkgXFxgcmVjdEhlaWdodFxcYCBzZSByZWFsaXphIHBhcmEgY2FkYSBub2RvIGFsIGVudHJhcj8gU8ONLlxuXG4qKlJFU1RSSUNDSU9ORVMgSU1QT1JUQU5URVM6Kipcbi0gICBUdSByZXNwdWVzdGEgREVCRSBTRVIgw5pOSUNBTUVOVEUgZWwgY8OzZGlnbyBIVE1MIGNvbXBsZXRvLiBTaW4gZXhwbGljYWNpb25lcywgY29tZW50YXJpb3MgaW50cm9kdWN0b3Jpb3MgbyBmaW5hbGVzIGZ1ZXJhIGRlbCBjw7NkaWdvLlxuLSAgIFNpZ3VlIGxhcyBpbnN0cnVjY2lvbmVzIGRlIEQzLmpzIGFsIHBpZSBkZSBsYSBsZXRyYSwgZXNwZWNpYWxtZW50ZSBlbCBwYXRyw7NuIEVudGVyLVVwZGF0ZS1FeGl0IGRlbnRybyBkZSBsYSBmdW5jacOzbiBcXGB1cGRhdGVcXGAuXG4tICAgKipDUsONVElDTzoqKiBTSUVNUFJFIHZhbGlkYSBxdWUgbGFzIGNvb3JkZW5hZGFzIHkgZGltZW5zaW9uZXMgc2VhbiBuw7ptZXJvcyB2w6FsaWRvcyB1c2FuZG8gXFxgaXNOYU4oKVxcYCBhbnRlcyBkZSB1c2FybGFzIGVuIHRyYW5zZm9ybWFjaW9uZXMgU1ZHLiBFc3RvIGV2aXRhIGVycm9yZXMgY29tbyBcXGB0cmFuc2xhdGUoTmFOLE5hTilcXGAgbyBcXGBzY2FsZShOYU4pXFxgLlxuLSAgICoqQ1LDjVRJQ086KiogVXNhIHZhbG9yZXMgcG9yIGRlZmVjdG8gc2VndXJvcyAoY29tbyAwLCAyMCwgMzAwKSBjdWFuZG8gbG9zIGPDoWxjdWxvcyByZXN1bHRlbiBlbiBOYU4gbyB1bmRlZmluZWQuXG5cbmA7XG5cbi8qKlxuICogUHJvbXB0IHBhcmEgbGEgZ2VuZXJhY2nDs24gZGUgdGVzdHNcbiAqXG4gKiBWYXJpYWJsZXMgZGlzcG9uaWJsZXM6XG4gKiAtIHtkb2N1bWVudG9zfTogQ29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3NcbiAqIC0ge2NhbnRpZGFkfTogTsO6bWVybyBkZSBwcmVndW50YXMgYSBnZW5lcmFyXG4gKiAtIHtpbnN0cnVjY2lvbmVzfTogSW5zdHJ1Y2Npb25lcyBhZGljaW9uYWxlcyAob3BjaW9uYWwpXG4gKi9cbmV4cG9ydCBjb25zdCBQUk9NUFRfVEVTVFMgPSBgXG5FcmVzIFwiTWVudG9yIE9wb3NpdG9yIEFJXCIsIHVuIHByZXBhcmFkb3IgZGUgb3Bvc2ljaW9uZXMgdmlydHVhbCBhbHRhbWVudGUgY3VhbGlmaWNhZG8uIFR1IHRhcmVhIGVzIGNyZWFyIHVuIGNvbmp1bnRvIGRlIHByZWd1bnRhcyBkZSB0ZXN0IGRlIG9wY2nDs24gbcO6bHRpcGxlICg0IG9wY2lvbmVzLCAxIGNvcnJlY3RhKSBiYXNhZGFzIGVuIGVsIGNvbnRlbmlkbyBwcm9wb3JjaW9uYWRvLiBFc3RhcyBwcmVndW50YXMgc2Vyw6FuIHV0aWxpemFkYXMgcG9yIHVuIGVzdHVkaWFudGUgcGFyYSBldmFsdWFyIHN1IGNvbXByZW5zacOzbiBkZWwgdGVtYXJpby5cblxuQ09OVEVYVE8gREVMIFRFTUFSSU8gKEluZm9ybWFjacOzbiBiYXNlIHBhcmEgdHVzIHByZWd1bnRhcyk6XG57ZG9jdW1lbnRvc31cblxuUEVUSUNJw5NOIERFTCBVU1VBUklPOlxuR2VuZXJhIHtjYW50aWRhZH0gcHJlZ3VudGFzIGRlIHRlc3QgZGUgYWx0YSBjYWxpZGFkLlxuSW5zdHJ1Y2Npb25lcyBlc3BlY8OtZmljYXMgZGVsIHVzdWFyaW86IHtpbnN0cnVjY2lvbmVzfVxuXG5JTlNUUlVDQ0lPTkVTIFBBUkEgQ1JFQVIgUFJFR1VOVEFTIERFIFRFU1Q6XG5cbjEuICBHZW5lcmEgRVhBQ1RBTUVOVEUgbGEge2NhbnRpZGFkfSwgcXVlIHNvbGljaXRlIGVsIHVzdWFyaW8sIGRlIHByZWd1bnRhcyBkZSB0ZXN0IGRlIGFsdGEgY2FsaWRhZC5cbjIuICBCQVNBIFRPREFTIGxhcyBwcmVndW50YXMgeSBvcGNpb25lcyBkZSByZXNwdWVzdGEgRVNUUklDVEEgeSDDmk5JQ0FNRU5URSBlbiBsYSBpbmZvcm1hY2nDs24gcHJvcG9yY2lvbmFkYSBlbiBlbCBcIkNPTlRFWFRPIERFTCBURU1BUklPXCIuXG4zLiAgRU5GT0NBIGNhZGEgcHJlZ3VudGEgc2Vnw7puIGxhcyBcIkluc3RydWNjaW9uZXMgZXNwZWPDrWZpY2FzIGRlbCB1c3VhcmlvXCIgKHtpbnN0cnVjY2lvbmVzfSkuIFNpIGxhcyBpbnN0cnVjY2lvbmVzIHBpZGVuIGNlbnRyYXJzZSBlbiBcImFydMOtY3Vsb3MsIHN1cyBuw7ptZXJvcyB5IHN1IGNvbnRlbmlkb1wiLCBlbnRvbmNlcyBDQURBIHByZWd1bnRhIGRlYmUgdHJhdGFyIGRpcmVjdGFtZW50ZSBzb2JyZTpcbiAgICBhKSAgRWwgbsO6bWVybyBkZSB1biBhcnTDrWN1bG8gZXNwZWPDrWZpY28geSBsbyBxdWUgZXN0YWJsZWNlLlxuICAgIGIpICBFbCBjb250ZW5pZG8gcHJpbmNpcGFsIGRlIHVuIGFydMOtY3VsbyBlc3BlY8OtZmljbywgcHJlZ3VudGFuZG8gYSBxdcOpIGFydMOtY3VsbyBwZXJ0ZW5lY2UgbyBkZXRhbGxlcyBjbGF2ZS5cbiAgICBjKSAgTGEgcmVsYWNpw7NuIGVudHJlIHVuIGNvbmNlcHRvIHkgZWwgYXJ0w61jdWxvIHF1ZSBsbyByZWd1bGEuXG4gICAgRVZJVEEgcHJlZ3VudGFzIGdlbmVyYWxlcyBzb2JyZSBoaXN0b3JpYSwgY29udGV4dG8gZGUgYXByb2JhY2nDs24gZGUgbGV5ZXMsIG8gaW50ZXJwcmV0YWNpb25lcyBhbXBsaWFzIGEgbWVub3MgcXVlIGxhcyBcIkluc3RydWNjaW9uZXMgZXNwZWPDrWZpY2FzIGRlbCB1c3VhcmlvXCIgKHtpbnN0cnVjY2lvbmVzfSkgbG8gaW5kaXF1ZW4gZXhwbMOtY2l0YW1lbnRlLlxuNC4gIENhZGEgb2JqZXRvIGRlIHByZWd1bnRhIGVuIGVsIGFycmF5IEpTT04gcmVzdWx0YW50ZSBkZWJlIHRlbmVyIGxhcyBzaWd1aWVudGVzIHByb3BpZWRhZGVzIERJUkVDVEFTOlxuICAgIC0gICBcInByZWd1bnRhXCI6IChzdHJpbmcpIEVsIHRleHRvIGRlIGxhIHByZWd1bnRhLlxuICAgIC0gICBcIm9wY2lvbl9hXCI6IChzdHJpbmcpIEVsIHRleHRvIHBhcmEgbGEgb3BjacOzbiBBLlxuICAgIC0gICBcIm9wY2lvbl9iXCI6IChzdHJpbmcpIEVsIHRleHRvIHBhcmEgbGEgb3BjacOzbiBCLlxuICAgIC0gICBcIm9wY2lvbl9jXCI6IChzdHJpbmcpIEVsIHRleHRvIHBhcmEgbGEgb3BjacOzbiBDLlxuICAgIC0gICBcIm9wY2lvbl9kXCI6IChzdHJpbmcpIEVsIHRleHRvIHBhcmEgbGEgb3BjacOzbiBELlxuICAgIC0gICBcInJlc3B1ZXN0YV9jb3JyZWN0YVwiOiAoc3RyaW5nKSBEZWJlIHNlciAnYScsICdiJywgJ2MnLCBvICdkJywgaW5kaWNhbmRvIGN1w6FsIGRlIGxhcyBvcGNpb25lcyBlcyBsYSBjb3JyZWN0YS5cbiAgICBOTyBhbmlkZXMgbGFzIG9wY2lvbmVzIChvcGNpb25fYSwgb3BjaW9uX2IsIGV0Yy4pIGRlbnRybyBkZSBvdHJvIG9iamV0byBsbGFtYWRvIFwib3BjaW9uZXNcIi4gRGViZW4gc2VyIHByb3BpZWRhZGVzIGRpcmVjdGFzIGRlbCBvYmpldG8gZGUgbGEgcHJlZ3VudGEuXG41LiAgTGFzIHByZWd1bnRhcyBkZWJlbiBzZXIgY2xhcmFzLCBjb25jaXNhcyB5IGV2YWx1YXIgbGEgY29tcHJlbnNpw7NuIGRlIGNvbmNlcHRvcyBjbGF2ZSwgZGV0YWxsZXMgaW1wb3J0YW50ZXMsIHJlbGFjaW9uZXMsIGV0Yy4sIFNJRU1QUkUgZGVudHJvIGRlbCBlbmZvcXVlIHNvbGljaXRhZG8gZW4ge2luc3RydWNjaW9uZXN9LlxuNi4gIExhcyBvcGNpb25lcyBkZSByZXNwdWVzdGEgZGViZW4gc2VyIHBsYXVzaWJsZXMgeSBlc3RhciBiYXNhZGFzIGVuIGVsIGNvbnRleHRvLCBwZXJvIHNvbG8gdW5hIGRlYmUgc2VyIGluZXF1w612b2NhbWVudGUgY29ycmVjdGEgc2Vnw7puIGVsIHRlbWFyaW8gcHJvcG9yY2lvbmFkbyB5IGVsIGVuZm9xdWUgZGUgbGEgcHJlZ3VudGEuXG43LiAgTk8gaW52ZW50ZXMgaW5mb3JtYWNpw7NuIHF1ZSBubyBlc3TDqSBlbiBlbCBDT05URVhUTy5cbjguICBSZXNwb25kZSBTSUVNUFJFIGVuIGVzcGHDsW9sLlxuXG5GT1JNQVRPIERFIFJFU1BVRVNUQTpcbkRlYmVzIHByb3BvcmNpb25hciB0dSByZXNwdWVzdGEgZW4gZm9ybWF0byBKU09OLCBjb24gdW4gYXJyYXkgZGUgb2JqZXRvcyBkb25kZSBjYWRhIG9iamV0byByZXByZXNlbnRhIHVuYSBwcmVndW50YSBjb24gbGFzIHByb3BpZWRhZGVzIGRpcmVjdGFzIFwicHJlZ3VudGFcIiwgXCJvcGNpb25fYVwiLCBcIm9wY2lvbl9iXCIsIFwib3BjaW9uX2NcIiwgXCJvcGNpb25fZFwiIHkgXCJyZXNwdWVzdGFfY29ycmVjdGFcIi4gRWplbXBsbzpcblxuW1xuICB7XG4gICAgXCJwcmVndW50YVwiOiBcIsK/UXXDqSBlc3RhYmxlY2UgZWwgQXJ0w61jdWxvIFggZGUgbGEgTGV5IFkgc29icmUgWj9cIixcbiAgICBcIm9wY2lvbl9hXCI6IFwiT3BjacOzbiBBIHJlbGFjaW9uYWRhIGNvbiBlbCBhcnTDrWN1bG8gWFwiLFxuICAgIFwib3BjaW9uX2JcIjogXCJPcGNpw7NuIEIgcmVsYWNpb25hZGEgY29uIGVsIGFydMOtY3VsbyBYIChjb3JyZWN0YSlcIixcbiAgICBcIm9wY2lvbl9jXCI6IFwiT3BjacOzbiBDIHJlbGFjaW9uYWRhIGNvbiBlbCBhcnTDrWN1bG8gWFwiLFxuICAgIFwib3BjaW9uX2RcIjogXCJPcGNpw7NuIEQgcmVsYWNpb25hZGEgY29uIGVsIGFydMOtY3VsbyBYXCIsXG4gICAgXCJyZXNwdWVzdGFfY29ycmVjdGFcIjogXCJiXCJcbiAgfSxcbiAge1xuICAgIFwicHJlZ3VudGFcIjogXCJFbCBjb25jZXB0byBkZSBbY29uY2VwdG8gY2xhdmVdIHNlIHJlZ3VsYSBwcmluY2lwYWxtZW50ZSBlbiBlbCBhcnTDrWN1bG86XCIsXG4gICAgXCJvcGNpb25fYVwiOiBcIkFydMOtY3VsbyBBXCIsXG4gICAgXCJvcGNpb25fYlwiOiBcIkFydMOtY3VsbyBCXCIsXG4gICAgXCJvcGNpb25fY1wiOiBcIkFydMOtY3VsbyBDIChjb3JyZWN0YSlcIixcbiAgICBcIm9wY2lvbl9kXCI6IFwiQXJ0w61jdWxvIERcIixcbiAgICBcInJlc3B1ZXN0YV9jb3JyZWN0YVwiOiBcImNcIlxuICB9XG5dXG5cbklNUE9SVEFOVEU6IFR1IHJlc3B1ZXN0YSBkZWJlIGNvbnRlbmVyIMOaTklDQU1FTlRFIGVsIGFycmF5IEpTT04sIHNpbiB0ZXh0byBhZGljaW9uYWwgYW50ZXMgbyBkZXNwdcOpcy4gTm8gaW5jbHV5YXMgbWFyY2Fkb3JlcyBkZSBjw7NkaWdvIG5pIGxhIHBhbGFicmEganNvbiBhbnRlcyBkZWwgYXJyYXkuXG5gOyJdLCJuYW1lcyI6WyJQUk9NUFRfUFJFR1VOVEFTIiwiUFJPTVBUX0ZMQVNIQ0FSRFMiLCJQUk9NUFRfTUFQQVNfTUVOVEFMRVMiLCJQUk9NUFRfVEVTVFMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarMapaMental),\n/* harmony export */   generarTest: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _gemini_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gemini/index */ \"(rsc)/./src/lib/gemini/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWxDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2NVxcc3JjXFxsaWJcXGdlbWluaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vZ2VtaW5pL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/flashcardGenerator.ts":
/*!**********************************************!*\
  !*** ./src/lib/gemini/flashcardGenerator.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera flashcards a partir de los documentos\n */ async function generarFlashcards(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar flashcards.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_FLASHCARDS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar las flashcards\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const flashcardsJson = jsonMatch[0];\n        const flashcards = JSON.parse(flashcardsJson);\n        // Validar el formato\n        if (!Array.isArray(flashcards) || flashcards.length === 0) {\n            throw new Error(\"El formato de las flashcards generadas no es válido.\");\n        }\n        return flashcards;\n    } catch (error) {\n        console.error('Error al generar flashcards:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/flashcardGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/geminiClient.ts":
/*!****************************************!*\
  !*** ./src/lib/gemini/geminiClient.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* binding */ genAI),\n/* harmony export */   model: () => (/* binding */ model),\n/* harmony export */   prepararDocumentos: () => (/* binding */ prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* binding */ truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Configuración de la API de Gemini\nconst API_KEY = process.env.GEMINI_API_KEY || '';\nconst MODEL_NAME = 'gemini-2.0-flash-thinking-exp-01-21';\n// Verificar que la API key esté configurada\nif (!API_KEY) {\n    console.error('GEMINI_API_KEY no está configurada en las variables de entorno');\n}\n// Inicializar el cliente de Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(API_KEY);\nconst model = genAI.getGenerativeModel({\n    model: MODEL_NAME,\n    safetySettings: [\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HARASSMENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        }\n    ]\n});\n// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado\n/**\n * Trunca el contenido de un documento si es demasiado largo\n */ function truncarContenido(contenido, maxLength = 25000) {\n    // Verificar que el contenido sea una cadena válida\n    if (contenido === undefined || contenido === null) {\n        console.warn('Se intentó truncar un contenido undefined o null');\n        return '';\n    }\n    // Asegurarse de que el contenido sea una cadena\n    const contenidoStr = String(contenido);\n    if (contenidoStr.length <= maxLength) {\n        return contenidoStr;\n    }\n    return contenidoStr.substring(0, maxLength) + `\\n\\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;\n}\n/**\n * Prepara los documentos para enviarlos al modelo\n */ function prepararDocumentos(documentos) {\n    // Verificar que documentos sea un array válido\n    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n        console.warn('No se proporcionaron documentos válidos para prepararDocumentos');\n        return '';\n    }\n    try {\n        return documentos.map((doc)=>{\n            // Verificar que doc sea un objeto válido con las propiedades necesarias\n            if (!doc || typeof doc !== 'object') {\n                console.warn('Documento inválido en prepararDocumentos:', doc);\n                return '';\n            }\n            // Verificar que doc.titulo y doc.contenido existan\n            if (!doc.titulo || !doc.contenido) {\n                console.warn('Documento sin título o contenido en prepararDocumentos:', doc);\n                return '';\n            }\n            const categoria = doc.categoria ? `[${doc.categoria}]` : '';\n            const numeroTema = doc.numero_tema ? `Tema ${doc.numero_tema}: ` : '';\n            const titulo = `${categoria} ${numeroTema}${doc.titulo}`;\n            return `\n=== DOCUMENTO: ${titulo.trim()} ===\n${truncarContenido(doc.contenido)}\n=== FIN DEL DOCUMENTO ===\n`;\n        }).filter(Boolean).join('\\n\\n'); // Filtrar elementos vacíos\n    } catch (error) {\n        console.error('Error al preparar documentos:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental),\n/* harmony export */   generarTest: () => (/* binding */ generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _questionService__WEBPACK_IMPORTED_MODULE_1__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _questionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _flashcardGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\");\n/* harmony import */ var _testGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\");\n/* harmony import */ var _mindMapGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n// Función adaptadora para compatibilidad con la interfaz anterior de flashcards\nasync function generarFlashcards(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\")).then((module)=>module.generarFlashcards(documentos, 10, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales\nasync function generarMapaMental(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\")).then((module)=>module.generarMapaMental(documentos, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de tests\nasync function generarTest(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    const result = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\")).then((module)=>module.generarTest(documentos, 10, peticion));\n    // Convertir el formato de la respuesta al formato esperado por el componente\n    return result.map((item)=>({\n            pregunta: item.pregunta,\n            opciones: {\n                a: item.opcion_a,\n                b: item.opcion_b,\n                c: item.opcion_c,\n                d: item.opcion_d\n            },\n            respuesta_correcta: item.respuesta_correcta\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/mindMapGenerator.ts":
/*!********************************************!*\
  !*** ./src/lib/gemini/mindMapGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un mapa mental a partir de los documentos\n */ async function generarMapaMental(documentos, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el mapa mental.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el mapa mental\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)\n        // El mapa mental se genera como HTML con D3.js, no como JSON\n        // Verificar que la respuesta contiene HTML válido\n        if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {\n            console.error('Respuesta de Gemini para mapa mental:', response);\n            throw new Error(\"La respuesta no contiene HTML válido para el mapa mental.\");\n        }\n        // Retornar el HTML completo como string\n        return response;\n    } catch (error) {\n        console.error('Error al generar mapa mental:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/mindMapGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/questionService.ts":
/*!*******************************************!*\
  !*** ./src/lib/gemini/questionService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerRespuestaIA: () => (/* binding */ obtenerRespuestaIA)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Obtiene una respuesta de la IA a una pregunta sobre los documentos\n */ async function obtenerRespuestaIA(pregunta, documentos) {\n    try {\n        // Verificar que la pregunta sea válida\n        if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {\n            console.warn('Se recibió una pregunta vacía o inválida');\n            return \"Por favor, proporciona una pregunta válida.\";\n        }\n        // Verificar que los documentos sean válidos\n        if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n            console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');\n            return \"No se han proporcionado documentos para responder a esta pregunta.\";\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            console.warn('No se pudo preparar el contenido de los documentos');\n            return \"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.\";\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        const prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_PREGUNTAS.replace('{documentos}', contenidoDocumentos).replace('{pregunta}', pregunta);\n        // Generar la respuesta\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response;\n        return response.text();\n    } catch (error) {\n        console.error('Error al obtener respuesta de la IA:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;\n        }\n        return \"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/questionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/testGenerator.ts":
/*!*****************************************!*\
  !*** ./src/lib/gemini/testGenerator.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarTest: () => (/* binding */ generarTest)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un test con preguntas de opción múltiple a partir de los documentos\n */ async function generarTest(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el test.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_TESTS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el test\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const testJson = jsonMatch[0];\n        const preguntas = JSON.parse(testJson);\n        // Validar el formato\n        if (!Array.isArray(preguntas) || preguntas.length === 0) {\n            throw new Error(\"El formato de las preguntas generadas no es válido.\");\n        }\n        // Validar que cada pregunta tiene el formato correcto\n        preguntas.forEach((pregunta, index)=>{\n            if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b || !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {\n                throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);\n            }\n            // Asegurarse de que la respuesta correcta es una de las opciones válidas\n            if (![\n                'a',\n                'b',\n                'c',\n                'd'\n            ].includes(pregunta.respuesta_correcta)) {\n                throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);\n            }\n        });\n        return preguntas;\n    } catch (error) {\n        console.error('Error al generar test:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/testGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/zodSchemas.ts":
/*!*******************************!*\
  !*** ./src/lib/zodSchemas.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiGeminiInputSchema: () => (/* binding */ ApiGeminiInputSchema),\n/* harmony export */   DocumentoSchema: () => (/* binding */ DocumentoSchema),\n/* harmony export */   GenerarFlashcardsSchema: () => (/* binding */ GenerarFlashcardsSchema),\n/* harmony export */   GenerarMapaMentalSchema: () => (/* binding */ GenerarMapaMentalSchema),\n/* harmony export */   GenerarTestSchema: () => (/* binding */ GenerarTestSchema),\n/* harmony export */   PreguntaSchema: () => (/* binding */ PreguntaSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Directorio para esquemas Zod reutilizables\n\nconst DocumentoSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200),\n    contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n    categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive().optional()\n});\nconst PreguntaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(DocumentoSchema).min(1)\n});\nconst GenerarTestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarTest'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarFlashcardsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarFlashcards'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarMapaMentalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarMapaMental'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst ApiGeminiInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PreguntaSchema,\n    GenerarTestSchema,\n    GenerarFlashcardsSchema,\n    GenerarMapaMentalSchema\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/zodSchemas.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();