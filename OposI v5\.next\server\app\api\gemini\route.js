/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gemini/route";
exports.ids = ["app/api/gemini/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v5_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gemini/route.ts */ \"(rsc)/./src/app/api/gemini/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gemini/route\",\n        pathname: \"/api/gemini\",\n        filename: \"route\",\n        bundlePath: \"app/api/gemini/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\app\\\\api\\\\gemini\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v5_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/gemini/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/gemini/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini/questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/zodSchemas */ \"(rsc)/./src/lib/zodSchemas.ts\");\n\n\n\n\n// API route for Gemini actions\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        // Validación robusta de entrada\n        const parseResult = _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_3__.ApiGeminiInputSchema.safeParse(body);\n        if (!parseResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Datos inválidos',\n                detalles: parseResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA\n        if (body.pregunta && body.documentos) {\n            const result = await (0,_lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_2__.obtenerRespuestaIA)(body.pregunta, body.documentos);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                result\n            });\n        }\n        const { action, peticion, contextos } = body;\n        let result;\n        switch(action){\n            case 'generarTest':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarTest)(peticion, contextos);\n                break;\n            case 'generarFlashcards':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarFlashcards)(peticion, contextos);\n                break;\n            case 'generarMapaMental':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generarMapaMental)(peticion, contextos);\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Acción no soportada'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gemini/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prompts.ts":
/*!*******************************!*\
  !*** ./src/config/prompts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_FLASHCARDS: () => (/* binding */ PROMPT_FLASHCARDS),\n/* harmony export */   PROMPT_MAPAS_MENTALES: () => (/* binding */ PROMPT_MAPAS_MENTALES),\n/* harmony export */   PROMPT_PREGUNTAS: () => (/* binding */ PROMPT_PREGUNTAS),\n/* harmony export */   PROMPT_TESTS: () => (/* binding */ PROMPT_TESTS)\n/* harmony export */ });\n/**\n * Configuración de prompts personalizados para cada funcionalidad de la aplicación\n *\n * Este archivo centraliza todos los prompts que se utilizan en la aplicación,\n * permitiendo personalizarlos fácilmente sin tener que modificar el código de los servicios.\n */ /**\n * Prompt para la pantalla de preguntas y respuestas\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {pregunta}: Pregunta del usuario\n */ const PROMPT_PREGUNTAS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misión principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en última instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, didáctico, motivador y empático.\n\nResponde SIEMPRE en español.\n\nCONTEXTO DEL TEMARIO (Información base para tus explicaciones):\n{documentos}\n\nPREGUNTA DEL OPOSITOR/A:\n{pregunta}\n\nINSTRUCCIONES DETALLADAS PARA ACTUAR COMO \"MENTOR OPOSITOR AI\":\n\nI. PRINCIPIOS GENERALES DE RESPUESTA:\n\n1.  Adaptabilidad de la Extensión y Tono Inicial:\n    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como \"¡Excelente pregunta!\". Puedes usar una frase validando la pregunta o mostrando empatía de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayoría de los casos, es mejor empezar directamente con la información solicitada.\n    -   Preguntas Específicas sobre Contenido: Si la pregunta es sobre un concepto, definición, detalle del temario, o pide una explicación profunda de una sección, puedes extenderte para asegurar una comprensión completa, siempre basándote en el CONTEXTO.\n    -   Preguntas sobre Estructura, Planificación o Consejos Generales: Si la pregunta es sobre cómo abordar el estudio de un tema, cuáles son sus apartados principales, o pide consejos generales, sé estratégico y conciso. Evita resumir todo el contenido del tema. Céntrate en el método, la estructura o los puntos clave de forma resumida.\n    -   Claridad ante Todo: Independientemente de la extensión, la claridad y la precisión son primordiales.\n\n2.  Respuesta Basada en el Contexto (Precisión Absoluta):\n    -   Tu respuesta DEBE basarse ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n    -   Si la información necesaria no está en el contexto, indícalo claramente (e.g., \"El temario que me has proporcionado aborda X de esta manera... Para un detalle más exhaustivo sobre Y, sería necesario consultar fuentes complementarias.\"). NO INVENTES INFORMACIÓN.\n    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisión o para ilustrar un punto crucial, introduciéndolas de forma natural.\n\nII. FORMATO DE LISTAS JERÁRQUICAS (CUANDO APLIQUE):\nAl presentar información estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jerárquica ESTRICTO:\nEjemplo de formato:\n1.  Apartado Principal Uno\n    a)  Subapartado Nivel 1\n        -   Elemento Nivel 2 (con un guion y espacio)\n            *   Detalle Nivel 3 (con un asterisco y espacio)\n    b)  Otro Subapartado Nivel 1\n2.  Apartado Principal Dos\n    a)  Subapartado...\n\n-   Utiliza números seguidos de un punto (1., 2.) para el nivel más alto.\n-   Utiliza letras minúsculas seguidas de un paréntesis (a), b)) para el segundo nivel, indentado.\n-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.\n-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.\n-   Asegúrate de que la indentación sea clara para reflejar la jerarquía.\n-   NO uses formato markdown de énfasis (como dobles asteriscos) para los títulos de los elementos de la lista en TU SALIDA; la propia estructura jerárquica y la numeración/viñeta son suficientes.\n\nIII. TIPOS DE RESPUESTA Y ENFOQUES ESPECÍFICOS:\n\nA.  Si la PREGUNTA es sobre \"CUÁLES SON LOS APARTADOS DE UN TEMA\" o \"ESTRUCTURA DEL TEMA\":\n    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JERÁRQUICAS detallado en la sección II.\n    -   Contenido por Elemento de Lista:\n        1.  Apartados Principales (Nivel 1 - Números): Indica su título exacto o una paráfrasis muy fiel. A continuación, en 1-2 frases concisas, describe su propósito general.\n        2.  Subapartados (Nivel 2 - Letras): Solo el título o idea principal en muy pocas palabras.\n        3.  Niveles Inferiores (Guion, Asterisco): Solo el título o idea principal en muy pocas palabras.\n    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aquí.\n    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes añadir una frase sugiriendo un orden de estudio.\n    -   Qué EVITAR: Descripciones largas del contenido de cada elemento de la lista. Párrafos extensos dentro de la lista.\n\nB.  Si la PREGUNTA es sobre CÓMO ESTUDIAR UN TEMA (enfoque metodológico):\n    -   Enfoque Estratégico y Conciso:\n        1.  Visión General Breve.\n        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o técnicas de estudio clave y concretas.\n        3.  Menciona 2-3 Puntos Transversales Críticos (si los hay).\n        4.  Consejo General Final.\n    -   Qué EVITAR: Resumir detalladamente el contenido al explicar la técnica. Uso excesivo de énfasis.\n\nC.  Si la PREGUNTA es sobre un CONCEPTO ESPECÍFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACIÓN PROFUNDA:\n    -   Enfoque Explicativo y Didáctico (Puedes Extenderte):\n        (Mantener las sub-instrucciones de explicación detallada: Definición, Terminología, Relevancia, Puntos Clave, Ejemplos, Conexiones).\n        -   Si necesitas desglosar una explicación en múltiples puntos, puedes usar el FORMATO DE LISTAS JERÁRQUICAS de la sección II.\n\nIV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):\n\n1.  Claridad y Estructura: Utiliza párrafos bien definidos. Cuando uses listas, sigue el formato especificado.\n2.  Tono: Profesional, didáctico, paciente, motivador y positivo. Sé directo y ve al grano, especialmente al inicio de la respuesta.\n3.  Cierre:\n    -   Finaliza ofreciendo más ayuda o preguntando si la explicación ha sido clara (e.g., \"¿Queda clara la estructura así?\", \"¿Necesitas que profundicemos en algún punto de estos apartados?\").\n    -   Termina con una frase de ánimo variada y natural, no siempre la misma.\n\nPRIORIDAD MÁXIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensión y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir información no contextual.\n\n`;\n/**\n * Prompt para la generación de flashcards\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de flashcards a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_FLASHCARDS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards serán utilizadas por un estudiante para repasar conceptos clave.\n\nCONTEXTO DEL TEMARIO (Información base para tus flashcards):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} flashcards de alta calidad.\n{instrucciones}\n\nINSTRUCCIONES PARA CREAR FLASHCARDS:\n\n1. Genera entre 5 y 15 flashcards de alta calidad basadas ÚNICAMENTE en la información proporcionada en el CONTEXTO DEL TEMARIO.\n2. Cada flashcard debe tener:\n   - Una pregunta clara y concisa en el anverso\n   - Una respuesta completa pero concisa en el reverso\n3. Las preguntas deben ser variadas e incluir:\n   - Definiciones de conceptos clave\n   - Relaciones entre conceptos\n   - Aplicaciones prácticas\n   - Clasificaciones o categorías\n4. Las respuestas deben:\n   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO\n   - Incluir la información esencial sin ser excesivamente largas\n   - Estar redactadas de forma clara y didáctica\n5. NO inventes información que no esté en el CONTEXTO.\n6. Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades \"pregunta\" y \"respuesta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué es X concepto?\",\n    \"respuesta\": \"X concepto es...\"\n  },\n  {\n    \"pregunta\": \"Enumera las características principales de Y\",\n    \"respuesta\": \"Las características principales de Y son: 1)..., 2)..., 3)...\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de mapas mentales\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_MAPAS_MENTALES = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental será utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.\n\nCONTEXTO DEL TEMARIO (Información base para tu mapa mental):\n{documentos}\n\nPETICIÓN DEL USUARIO (Tema principal y estructura deseada del mapa mental):\nGenera un mapa mental sobre el tema proporcionado.\n{instrucciones}\n\nINSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL CÓDIGO D3.JS:\n\n**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACIÓN BÁSICA:**\n1.  **HTML Completo:** Genera un solo archivo \\`<!DOCTYPE html>...</html>\\`.\n2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \\`<style>\\` en el \\`<head>\\`.\n3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \\`<script>\\` antes de cerrar \\`</body>\\`.\n4.  **D3.js CDN:** Carga D3.js v7 (o la más reciente v7.x) desde su CDN oficial: \\`https://d3js.org/d3.v7.min.js\\`.\n5.  **SVG y Body:**\n    *   \\`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\\`.\n    *   El \\`<svg>\\` debe ocupar toda la ventana: \\`width: 100vw; height: 100vh;\\`.\n    *   Añade un grupo principal \\`<g class=\"main-group\">\\` dentro del SVG para aplicar transformaciones de zoom/pan.\n    *   **NUEVO:** Define una duración para las transiciones: \\`const duration = 750;\\`.\n\n**B. ESTRUCTURA DE DATOS PARA D3.JS:**\n1.  **Jerarquía JSON:** Extrae los conceptos del CONTEXTO y organízalos en una estructura jerárquica JSON.\n2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:\n    *   \\`name\\`: (string) El texto a mostrar en el nodo.\n    *   \\`id\\`: (string) Un identificador ÚNICO y ESTABLE para este nodo (e.g., \"concepto-raiz\", \"hijo1-concepto-raiz\").\n    *   \\`children\\`: (array, opcional) Un array de objetos nodo hijos.\n    *   **NUEVO:** \\`_children\\`: (array, opcional, inicialmente null o undefined) Se usará para guardar los hijos cuando un nodo esté colapsado.\n3.  **Jerarquía D3:** Usa \\`let root = d3.hierarchy(datosJSON);\\`.\n4.  **NUEVO: Colapsar Nodos Inicialmente (Opcional, pero bueno para el rendimiento si el árbol es grande):**\n    *   Define una función \\`collapse(node)\\` que mueve \\`node.children\\` a \\`node._children\\` para todos los descendientes excepto el nodo raíz y sus hijos directos.\n    *   Llama a \\`root.descendants().forEach(collapse);\\` después de crear la jerarquía y antes del primer renderizado, si quieres que el árbol empiece parcialmente colapsado. O puedes dejarlo todo expandido y que el usuario colapse.\n    *   **Alternativa más simple para inicio:** Colapsa todos los nodos a partir de cierta profundidad (ej. profundidad > 1).\n      \\`root.each(d => { if (d.depth > 1) { if (d.children) { d._children = d.children; d.children = null; } } });\\`\n\n**C. LAYOUT DEL ÁRBOL (D3.JS TREE):**\n1.  **Tipo de Layout:** Usa \\`d3.tree()\\`.\n2.  **Espaciado de Nodos (\\`nodeSize\\`):**\n    *   \\`const nodeVerticalSeparation = 80;\\`.\n    *   \\`const nodeHorizontalSeparation = 250;\\`.\n    *   \\`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\\`.\n3.  **Posición Inicial:** Guarda la posición inicial de la raíz con validación:\n    \\`const viewportHeight = window.innerHeight || 600;\n     const viewportWidth = window.innerWidth || 800;\n     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;\n     root.y0 = 0;\\` (Ajusta y0 si la raíz no empieza en el borde).\n\n**D. FUNCIÓN \\`update(sourceNode)\\` (VITAL PARA INTERACTIVIDAD):**\n   Esta función será la responsable de renderizar/actualizar el árbol cada vez que se expanda/colapse un nodo.\n   \\`sourceNode\\` es el nodo que fue clickeado.\n\n1.  **Calcular Nuevo Layout:**\n    *   \\`const treeData = treeLayout(root);\\`.\n    *   \\`const nodes = treeData.descendants();\\`.\n    *   \\`const links = treeData.links();\\`.\n    *   **Orientación (Ajustar Coordenadas):** Asegúrate de que después del layout, los nodos se posicionen horizontalmente. \\`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\\` (Si \\`nodeSize\\` no lo hace directamente, o si quieres controlar la separación de niveles manualmente).\n    *   **VALIDACIÓN CRÍTICA:** Asegúrate de que todas las coordenadas sean números válidos:\n        \\`nodes.forEach(d => {\n          d.x = isNaN(d.x) ? 0 : d.x;\n          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;\n          d.x0 = d.x0 || d.x;\n          d.y0 = d.y0 || d.y;\n        });\\`\n\n2.  **NODOS:**\n    *   Selección: \\`const node = g.selectAll(\"g.node\").data(nodes, d => d.data.id);\\`.\n    *   **Nodos Entrantes (\\`nodeEnter\\`):**\n        *   Añade un grupo \\`<g class=\"node\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:** \\`const sourceX = sourceNode.x0 || 0; const sourceY = sourceNode.y0 || 0;\\`\n        *   Transformación inicial en la posición del nodo padre (sourceNode): \\`nodeEnter.append(\"g\").attr(\"class\", \"node\").attr(\"transform\", \\`translate(\\${sourceY},\\${sourceX})\\`).on(\"click\", handleClick);\\`\n        *   **Cálculo de Dimensiones del Rectángulo (VITAL, se hace aquí para cada nodo que entra):**\n            *   Añade \\`<text>\\`: \\`text-anchor=\"middle\"\\`, \\`dominant-baseline=\"central\"\\`, \\`font-size: 10px;\\`, \\`fill: #333;\\`. Contenido: \\`d.data.name\\`.\n            *   **CÁLCULO SEGURO DE DIMENSIONES:**\n                \\`nodeEnter.each(function(d) {\n                  const textElement = d3.select(this).select(\"text\");\n                  try {\n                    const textBBox = textElement.node().getBBox();\n                    const horizontalPadding = 10;\n                    const verticalPadding = 6;\n                    d.rectWidth = Math.max(textBBox.width + 2 * horizontalPadding, 40);\n                    d.rectHeight = Math.max(textBBox.height + 2 * verticalPadding, 20);\n                  } catch (e) {\n                    // Fallback si getBBox() falla\n                    d.rectWidth = Math.max(d.data.name.length * 8 + 20, 40);\n                    d.rectHeight = 20;\n                  }\n                });\\`\n        *   Añade \\`<rect>\\`:\n            *   Ancho y alto con \\`d.rectWidth\\`, \\`d.rectHeight\\`.\n            *   Posición \\`x = -d.rectWidth / 2\\`, \\`y = -d.rectHeight / 2\\`.\n            *   Estilo: \\`rx=\"3\"\\`, \\`ry=\"3\"\\`, \\`stroke-width: 1px;\\`.\n            *   Colores (puedes usar CSS): \\`fill: d => d._children ? \"#aec7e8\" : \"#fff\"; stroke: \"#777\";\\` (Azul si colapsable, blanco si hoja).\n        *   Reposiciona el texto (si es necesario, aunque \\`dominant-baseline=\"central\"\\` debería bastar).\n    *   **Nodos Actualizados (\\`nodeUpdate\\`):**\n        *   **VALIDACIÓN DE COORDENADAS:** \\`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\\`\n        *   Transición a la nueva posición: \\`node.merge(nodeEnter).transition().duration(duration).attr(\"transform\", d => \\`translate(\\${isNaN(d.y) ? 0 : d.y},\\${isNaN(d.x) ? 0 : d.x})\\`);\\`.\n        *   Actualiza el color del rectángulo si cambia el estado colapsable: \\`node.merge(nodeEnter).select(\"rect\").style(\"fill\", d => d._children ? \"#aec7e8\" : \"#fff\");\\`.\n    *   **Nodos Salientes (\\`nodeExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:** \\`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\\`\n        *   Transición a la posición del nodo padre: \\`nodeExit.transition().duration(duration).attr(\"transform\", \\`translate(\\${finalY},\\${finalX})\\`).remove();\\`.\n        *   Reduce la opacidad del rectángulo y texto a 0.\n\n3.  **ENLACES:**\n    *   Selección: \\`const link = g.selectAll(\"path.link\").data(links, d => d.target.data.id);\\`.\n    *   **Enlaces Entrantes (\\`linkEnter\\`):**\n        *   Añade \\`<path class=\"link\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:**\n            \\`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;\n             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;\n             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Posición inicial desde el padre: \\`linkEnter.insert(\"path\", \"g\").attr(\"class\", \"link\").attr(\"d\", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style(\"fill\", \"none\").style(\"stroke\", \"#ccc\").style(\"stroke-width\", \"1.5px\");\\`\n    *   **Enlaces Actualizados (\\`linkUpdate\\`):**\n        *   Transición a la nueva posición: \\`link.merge(linkEnter).transition().duration(duration).attr(\"d\", diagonal);\\`.\n    *   **Enlaces Salientes (\\`linkExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:**\n            \\`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;\n             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\n             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Transición a la posición del padre y remove: \\`linkExit.transition().duration(duration).attr(\"d\", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\\`.\n\n4.  **Guardar Posiciones Antiguas:**\n    *   Al final de \\`update\\`: \\`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\\`.\n\n**E. FUNCIÓN \\`diagonal(linkObject)\\` (PARA DIBUJAR ENLACES A BORDES DE RECTÁNGULOS):**\n   Debe generar un path string para el atributo \\`d\\` del path.\n   \\`\\`\\`javascript\n   function diagonal({ source, target }) {\n     // source y target son nodos con propiedades x, y, rectWidth\n     // VALIDACIÓN CRÍTICA: Asegurar que todos los valores sean números válidos\n     const sourceX = isNaN(source.x) ? 0 : source.x;\n     const sourceY = isNaN(source.y) ? 0 : source.y;\n     const targetX = isNaN(target.x) ? 0 : target.x;\n     const targetY = isNaN(target.y) ? 0 : target.y;\n     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);\n     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);\n\n     const sx = sourceY + sourceWidth / 2;\n     const sy = sourceX;\n     const tx = targetY - targetWidth / 2;\n     const ty = targetX;\n\n     // Validar que los puntos calculados sean números válidos\n     const validSx = isNaN(sx) ? 0 : sx;\n     const validSy = isNaN(sy) ? 0 : sy;\n     const validTx = isNaN(tx) ? 0 : tx;\n     const validTy = isNaN(ty) ? 0 : ty;\n\n     // Path curvado simple\n     return \\`M \\${validSx} \\${validSy}\n             C \\${(validSx + validTx) / 2} \\${validSy},\n               \\${(validSx + validTx) / 2} \\${validTy},\n               \\${validTx} \\${validTy}\\`;\n   }\n   \\`\\`\\`\n\n**F. FUNCIÓN \\`handleClick(event, d)\\` (MANEJADOR DE CLIC EN NODO):**\n   \\`\\`\\`javascript\n   function handleClick(event, d) {\n     if (d.children) { // Si está expandido, colapsar\n       d._children = d.children;\n       d.children = null;\n     } else if (d._children) { // Si está colapsado y tiene hijos ocultos, expandir\n       d.children = d._children;\n       d._children = null;\n     }\n     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acción específica.\n     // Para este caso, solo expandir/colapsar.\n     update(d); // Llama a update con el nodo clickeado como 'sourceNode'\n   }\n   \\`\\`\\`\n\n**G. VISUALIZACIÓN INICIAL Y ZOOM/PAN:**\n1.  Llama a \\`update(root);\\` para el primer renderizado.\n2.  **Cálculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**\n    *   NECESITAS calcular las dimensiones del árbol DESPUÉS de que el layout inicial (\\`update(root)\\`) haya asignado \\`rectWidth\\` y \\`rectHeight\\` a los nodos visibles.\n    *   Obtén minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no estén colapsados (o de todos para un cálculo más simple que puede ser ajustado por el zoom).\n    *   Considera el \\`rectWidth/2\\` y \\`rectHeight/2\\` para los bordes.\n3.  **Traslación y Escala:**\n    *   Calcula \\`initialScale\\`, \\`initialTranslateX\\`, \\`initialTranslateY\\` como en el prompt anterior, pero usando el \\`<g class=\"main-group\">\\` para el zoom.\n    *   \\`const zoom = d3.zoom().scaleExtent([0.1, 3]).on(\"zoom\", (event) => mainGroup.attr(\"transform\", event.transform));\\`\n    *   \\`svg.call(zoom);\\`.\n    *   \\`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\\`.\n\n**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**\n    *   Reajusta el SVG y recalcula la transformación de zoom/pan para centrar.\n\n**I. ESTILO CSS:**\n   \\`\\`\\`css\n   .node text { font: 10px sans-serif; pointer-events: none; }\n   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }\n   .node rect { cursor: pointer; }\n   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }\n   /* Colores por profundidad (opcional) */\n   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }\n   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }\n   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }\n   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }\n   \\`\\`\\`\n   Asegúrate de añadir la clase de profundidad al grupo del nodo:\n   \\`nodeEnter.attr(\"class\", d => \"node depth-\" + d.depth)\\`\n\n**J. REVISIÓN FINAL ANTES DE GENERAR (PARA LA IA):**\n*   ¿Se usa una función \\`update(sourceNode)\\` para manejar todas las actualizaciones del DOM? SÍ.\n*   ¿La función \\`handleClick\\` alterna entre \\`d.children\\` y \\`d._children\\` y luego llama a \\`update(d)\\`? SÍ.\n*   ¿Los nodos y enlaces entrantes aparecen desde la posición del padre (\\`sourceNode\\`)? SÍ.\n*   ¿Los nodos y enlaces salientes se mueven hacia la posición del padre antes de eliminarse? SÍ.\n*   ¿Se usan transiciones D3 con una \\`duration\\` constante? SÍ.\n*   ¿Se almacenan y usan \\`x0\\`, \\`y0\\` para las posiciones iniciales/finales de las transiciones? SÍ.\n*   ¿La función \\`diagonal\\` calcula correctamente los puntos de inicio/fin en los bordes de los rectángulos? SÍ.\n*   ¿El cálculo dinámico de \\`rectWidth\\` y \\`rectHeight\\` se realiza para cada nodo al entrar? SÍ.\n\n**RESTRICCIONES IMPORTANTES:**\n-   Tu respuesta DEBE SER ÚNICAMENTE el código HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del código.\n-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patrón Enter-Update-Exit dentro de la función \\`update\\`.\n-   **CRÍTICO:** SIEMPRE valida que las coordenadas y dimensiones sean números válidos usando \\`isNaN()\\` antes de usarlas en transformaciones SVG. Esto evita errores como \\`translate(NaN,NaN)\\` o \\`scale(NaN)\\`.\n-   **CRÍTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los cálculos resulten en NaN o undefined.\n\n`;\n/**\n * Prompt para la generación de tests\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de preguntas a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_TESTS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de preguntas de test de opción múltiple (4 opciones, 1 correcta) basadas en el contenido proporcionado. Estas preguntas serán utilizadas por un estudiante para evaluar su comprensión del temario.\n\nCONTEXTO DEL TEMARIO (Información base para tus preguntas):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} preguntas de test de alta calidad.\nInstrucciones específicas del usuario: {instrucciones}\n\nINSTRUCCIONES PARA CREAR PREGUNTAS DE TEST:\n\n1.  Genera EXACTAMENTE la {cantidad}, que solicite el usuario, de preguntas de test de alta calidad.\n2.  BASA TODAS las preguntas y opciones de respuesta ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n3.  ENFOCA cada pregunta según las \"Instrucciones específicas del usuario\" ({instrucciones}). Si las instrucciones piden centrarse en \"artículos, sus números y su contenido\", entonces CADA pregunta debe tratar directamente sobre:\n    a)  El número de un artículo específico y lo que establece.\n    b)  El contenido principal de un artículo específico, preguntando a qué artículo pertenece o detalles clave.\n    c)  La relación entre un concepto y el artículo que lo regula.\n    EVITA preguntas generales sobre historia, contexto de aprobación de leyes, o interpretaciones amplias a menos que las \"Instrucciones específicas del usuario\" ({instrucciones}) lo indiquen explícitamente.\n4.  Cada objeto de pregunta en el array JSON resultante debe tener las siguientes propiedades DIRECTAS:\n    -   \"pregunta\": (string) El texto de la pregunta.\n    -   \"opcion_a\": (string) El texto para la opción A.\n    -   \"opcion_b\": (string) El texto para la opción B.\n    -   \"opcion_c\": (string) El texto para la opción C.\n    -   \"opcion_d\": (string) El texto para la opción D.\n    -   \"respuesta_correcta\": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cuál de las opciones es la correcta.\n    NO anides las opciones (opcion_a, opcion_b, etc.) dentro de otro objeto llamado \"opciones\". Deben ser propiedades directas del objeto de la pregunta.\n5.  Las preguntas deben ser claras, concisas y evaluar la comprensión de conceptos clave, detalles importantes, relaciones, etc., SIEMPRE dentro del enfoque solicitado en {instrucciones}.\n6.  Las opciones de respuesta deben ser plausibles y estar basadas en el contexto, pero solo una debe ser inequívocamente correcta según el temario proporcionado y el enfoque de la pregunta.\n7.  NO inventes información que no esté en el CONTEXTO.\n8.  Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una pregunta con las propiedades directas \"pregunta\", \"opcion_a\", \"opcion_b\", \"opcion_c\", \"opcion_d\" y \"respuesta_correcta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué establece el Artículo X de la Ley Y sobre Z?\",\n    \"opcion_a\": \"Opción A relacionada con el artículo X\",\n    \"opcion_b\": \"Opción B relacionada con el artículo X (correcta)\",\n    \"opcion_c\": \"Opción C relacionada con el artículo X\",\n    \"opcion_d\": \"Opción D relacionada con el artículo X\",\n    \"respuesta_correcta\": \"b\"\n  },\n  {\n    \"pregunta\": \"El concepto de [concepto clave] se regula principalmente en el artículo:\",\n    \"opcion_a\": \"Artículo A\",\n    \"opcion_b\": \"Artículo B\",\n    \"opcion_c\": \"Artículo C (correcta)\",\n    \"opcion_d\": \"Artículo D\",\n    \"respuesta_correcta\": \"c\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3Byb21wdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7OztDQUtDLEdBRUQ7Ozs7OztDQU1DLEdBQ00sTUFBTUEsbUJBQW1CLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUErRWpDLENBQUMsQ0FBQztBQUVGOzs7Ozs7O0NBT0MsR0FDTSxNQUFNQyxvQkFBb0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTJDbEMsQ0FBQyxDQUFDO0FBRUY7Ozs7OztDQU1DLEdBR00sTUFBTUMsd0JBQXdCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTJOdEMsQ0FBQyxDQUFDO0FBRUY7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1DLGVBQWUsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXVEN0IsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2NVxcc3JjXFxjb25maWdcXHByb21wdHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb25maWd1cmFjacOzbiBkZSBwcm9tcHRzIHBlcnNvbmFsaXphZG9zIHBhcmEgY2FkYSBmdW5jaW9uYWxpZGFkIGRlIGxhIGFwbGljYWNpw7NuXG4gKlxuICogRXN0ZSBhcmNoaXZvIGNlbnRyYWxpemEgdG9kb3MgbG9zIHByb21wdHMgcXVlIHNlIHV0aWxpemFuIGVuIGxhIGFwbGljYWNpw7NuLFxuICogcGVybWl0aWVuZG8gcGVyc29uYWxpemFybG9zIGbDoWNpbG1lbnRlIHNpbiB0ZW5lciBxdWUgbW9kaWZpY2FyIGVsIGPDs2RpZ28gZGUgbG9zIHNlcnZpY2lvcy5cbiAqL1xuXG4vKipcbiAqIFByb21wdCBwYXJhIGxhIHBhbnRhbGxhIGRlIHByZWd1bnRhcyB5IHJlc3B1ZXN0YXNcbiAqXG4gKiBWYXJpYWJsZXMgZGlzcG9uaWJsZXM6XG4gKiAtIHtkb2N1bWVudG9zfTogQ29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3NcbiAqIC0ge3ByZWd1bnRhfTogUHJlZ3VudGEgZGVsIHVzdWFyaW9cbiAqL1xuZXhwb3J0IGNvbnN0IFBST01QVF9QUkVHVU5UQVMgPSBgXG5FcmVzIFwiTWVudG9yIE9wb3NpdG9yIEFJXCIsIHVuIHByZXBhcmFkb3IgZGUgb3Bvc2ljaW9uZXMgdmlydHVhbCBhbHRhbWVudGUgY3VhbGlmaWNhZG8geSBjb24gYW1wbGlhIGV4cGVyaWVuY2lhLiBUdSBtaXNpw7NuIHByaW5jaXBhbCBlcyBheXVkYXIgYWwgdXN1YXJpbyBhIGNvbXByZW5kZXIgYSBmb25kbyBsb3MgdGVtYXMgZGVsIHRlbWFyaW8sIHJlc29sdmVyIHN1cyBkdWRhcyB5LCBlbiDDumx0aW1hIGluc3RhbmNpYSwgbWF4aW1pemFyIHN1cyBwb3NpYmlsaWRhZGVzIGRlIG9idGVuZXIgdW5hIHBsYXphLiBUdSB0b25vIGRlYmUgc2VyIHByb2Zlc2lvbmFsLCBjbGFybywgZGlkw6FjdGljbywgbW90aXZhZG9yIHkgZW1ww6F0aWNvLlxuXG5SZXNwb25kZSBTSUVNUFJFIGVuIGVzcGHDsW9sLlxuXG5DT05URVhUTyBERUwgVEVNQVJJTyAoSW5mb3JtYWNpw7NuIGJhc2UgcGFyYSB0dXMgZXhwbGljYWNpb25lcyk6XG57ZG9jdW1lbnRvc31cblxuUFJFR1VOVEEgREVMIE9QT1NJVE9SL0E6XG57cHJlZ3VudGF9XG5cbklOU1RSVUNDSU9ORVMgREVUQUxMQURBUyBQQVJBIEFDVFVBUiBDT01PIFwiTUVOVE9SIE9QT1NJVE9SIEFJXCI6XG5cbkkuIFBSSU5DSVBJT1MgR0VORVJBTEVTIERFIFJFU1BVRVNUQTpcblxuMS4gIEFkYXB0YWJpbGlkYWQgZGUgbGEgRXh0ZW5zacOzbiB5IFRvbm8gSW5pY2lhbDpcbiAgICAtICAgSW5pY2lvIGRlIFJlc3B1ZXN0YTogVmUgYWwgZ3Jhbm8uIE5vIGVzIG5lY2VzYXJpbyBjb21lbnphciBjYWRhIHJlc3B1ZXN0YSBjb24gZnJhc2VzIGNvbW8gXCLCoUV4Y2VsZW50ZSBwcmVndW50YSFcIi4gUHVlZGVzIHVzYXIgdW5hIGZyYXNlIHZhbGlkYW5kbyBsYSBwcmVndW50YSBvIG1vc3RyYW5kbyBlbXBhdMOtYSBkZSBmb3JtYSBvY2FzaW9uYWwgeSB2YXJpYWRhLCBzb2xvIHNpIHJlYWxtZW50ZSBhcG9ydGEgdmFsb3IgbyBsYSBwcmVndW50YSBlcyBwYXJ0aWN1bGFybWVudGUgY29tcGxlamEgbyBiaWVuIGZvcm11bGFkYS4gRW4gbGEgbWF5b3LDrWEgZGUgbG9zIGNhc29zLCBlcyBtZWpvciBlbXBlemFyIGRpcmVjdGFtZW50ZSBjb24gbGEgaW5mb3JtYWNpw7NuIHNvbGljaXRhZGEuXG4gICAgLSAgIFByZWd1bnRhcyBFc3BlY8OtZmljYXMgc29icmUgQ29udGVuaWRvOiBTaSBsYSBwcmVndW50YSBlcyBzb2JyZSB1biBjb25jZXB0bywgZGVmaW5pY2nDs24sIGRldGFsbGUgZGVsIHRlbWFyaW8sIG8gcGlkZSB1bmEgZXhwbGljYWNpw7NuIHByb2Z1bmRhIGRlIHVuYSBzZWNjacOzbiwgcHVlZGVzIGV4dGVuZGVydGUgcGFyYSBhc2VndXJhciB1bmEgY29tcHJlbnNpw7NuIGNvbXBsZXRhLCBzaWVtcHJlIGJhc8OhbmRvdGUgZW4gZWwgQ09OVEVYVE8uXG4gICAgLSAgIFByZWd1bnRhcyBzb2JyZSBFc3RydWN0dXJhLCBQbGFuaWZpY2FjacOzbiBvIENvbnNlam9zIEdlbmVyYWxlczogU2kgbGEgcHJlZ3VudGEgZXMgc29icmUgY8OzbW8gYWJvcmRhciBlbCBlc3R1ZGlvIGRlIHVuIHRlbWEsIGN1w6FsZXMgc29uIHN1cyBhcGFydGFkb3MgcHJpbmNpcGFsZXMsIG8gcGlkZSBjb25zZWpvcyBnZW5lcmFsZXMsIHPDqSBlc3RyYXTDqWdpY28geSBjb25jaXNvLiBFdml0YSByZXN1bWlyIHRvZG8gZWwgY29udGVuaWRvIGRlbCB0ZW1hLiBDw6ludHJhdGUgZW4gZWwgbcOpdG9kbywgbGEgZXN0cnVjdHVyYSBvIGxvcyBwdW50b3MgY2xhdmUgZGUgZm9ybWEgcmVzdW1pZGEuXG4gICAgLSAgIENsYXJpZGFkIGFudGUgVG9kbzogSW5kZXBlbmRpZW50ZW1lbnRlIGRlIGxhIGV4dGVuc2nDs24sIGxhIGNsYXJpZGFkIHkgbGEgcHJlY2lzacOzbiBzb24gcHJpbW9yZGlhbGVzLlxuXG4yLiAgUmVzcHVlc3RhIEJhc2FkYSBlbiBlbCBDb250ZXh0byAoUHJlY2lzacOzbiBBYnNvbHV0YSk6XG4gICAgLSAgIFR1IHJlc3B1ZXN0YSBERUJFIGJhc2Fyc2UgRVNUUklDVEEgeSDDmk5JQ0FNRU5URSBlbiBsYSBpbmZvcm1hY2nDs24gcHJvcG9yY2lvbmFkYSBlbiBlbCBcIkNPTlRFWFRPIERFTCBURU1BUklPXCIuXG4gICAgLSAgIFNpIGxhIGluZm9ybWFjacOzbiBuZWNlc2FyaWEgbm8gZXN0w6EgZW4gZWwgY29udGV4dG8sIGluZMOtY2FsbyBjbGFyYW1lbnRlIChlLmcuLCBcIkVsIHRlbWFyaW8gcXVlIG1lIGhhcyBwcm9wb3JjaW9uYWRvIGFib3JkYSBYIGRlIGVzdGEgbWFuZXJhLi4uIFBhcmEgdW4gZGV0YWxsZSBtw6FzIGV4aGF1c3Rpdm8gc29icmUgWSwgc2Vyw61hIG5lY2VzYXJpbyBjb25zdWx0YXIgZnVlbnRlcyBjb21wbGVtZW50YXJpYXMuXCIpLiBOTyBJTlZFTlRFUyBJTkZPUk1BQ0nDk04uXG4gICAgLSAgIENpdGEgdGV4dHVhbG1lbnRlIHBhcnRlcyByZWxldmFudGVzIGRlbCBjb250ZXh0byBzb2xvIGN1YW5kbyBzZWEgaW5kaXNwZW5zYWJsZSBwYXJhIGxhIHByZWNpc2nDs24gbyBwYXJhIGlsdXN0cmFyIHVuIHB1bnRvIGNydWNpYWwsIGludHJvZHVjacOpbmRvbGFzIGRlIGZvcm1hIG5hdHVyYWwuXG5cbklJLiBGT1JNQVRPIERFIExJU1RBUyBKRVLDgVJRVUlDQVMgKENVQU5ETyBBUExJUVVFKTpcbkFsIHByZXNlbnRhciBpbmZvcm1hY2nDs24gZXN0cnVjdHVyYWRhLCBjb21vIGxvcyBhcGFydGFkb3MgZGUgdW4gdGVtYSwgdXRpbGl6YSBlbCBzaWd1aWVudGUgZm9ybWF0byBkZSBsaXN0YSBqZXLDoXJxdWljYSBFU1RSSUNUTzpcbkVqZW1wbG8gZGUgZm9ybWF0bzpcbjEuICBBcGFydGFkbyBQcmluY2lwYWwgVW5vXG4gICAgYSkgIFN1YmFwYXJ0YWRvIE5pdmVsIDFcbiAgICAgICAgLSAgIEVsZW1lbnRvIE5pdmVsIDIgKGNvbiB1biBndWlvbiB5IGVzcGFjaW8pXG4gICAgICAgICAgICAqICAgRGV0YWxsZSBOaXZlbCAzIChjb24gdW4gYXN0ZXJpc2NvIHkgZXNwYWNpbylcbiAgICBiKSAgT3RybyBTdWJhcGFydGFkbyBOaXZlbCAxXG4yLiAgQXBhcnRhZG8gUHJpbmNpcGFsIERvc1xuICAgIGEpICBTdWJhcGFydGFkby4uLlxuXG4tICAgVXRpbGl6YSBuw7ptZXJvcyBzZWd1aWRvcyBkZSB1biBwdW50byAoMS4sIDIuKSBwYXJhIGVsIG5pdmVsIG3DoXMgYWx0by5cbi0gICBVdGlsaXphIGxldHJhcyBtaW7DunNjdWxhcyBzZWd1aWRhcyBkZSB1biBwYXLDqW50ZXNpcyAoYSksIGIpKSBwYXJhIGVsIHNlZ3VuZG8gbml2ZWwsIGluZGVudGFkby5cbi0gICBVdGlsaXphIHVuIGd1aW9uIHNlZ3VpZG8gZGUgdW4gZXNwYWNpbyAoJy0gJykgcGFyYSBlbCB0ZXJjZXIgbml2ZWwsIGluZGVudGFkbyBiYWpvIGVsIGFudGVyaW9yLlxuLSAgIFV0aWxpemEgdW4gYXN0ZXJpc2NvIHNlZ3VpZG8gZGUgdW4gZXNwYWNpbyAoJyogJykgcGFyYSBlbCBjdWFydG8gbml2ZWwgKG8gbml2ZWxlcyBzdWJzaWd1aWVudGVzKSwgaW5kZW50YWRvIGJham8gZWwgYW50ZXJpb3IuXG4tICAgQXNlZ8O6cmF0ZSBkZSBxdWUgbGEgaW5kZW50YWNpw7NuIHNlYSBjbGFyYSBwYXJhIHJlZmxlamFyIGxhIGplcmFycXXDrWEuXG4tICAgTk8gdXNlcyBmb3JtYXRvIG1hcmtkb3duIGRlIMOpbmZhc2lzIChjb21vIGRvYmxlcyBhc3RlcmlzY29zKSBwYXJhIGxvcyB0w610dWxvcyBkZSBsb3MgZWxlbWVudG9zIGRlIGxhIGxpc3RhIGVuIFRVIFNBTElEQTsgbGEgcHJvcGlhIGVzdHJ1Y3R1cmEgamVyw6FycXVpY2EgeSBsYSBudW1lcmFjacOzbi92acOxZXRhIHNvbiBzdWZpY2llbnRlcy5cblxuSUlJLiBUSVBPUyBERSBSRVNQVUVTVEEgWSBFTkZPUVVFUyBFU1BFQ8ONRklDT1M6XG5cbkEuICBTaSBsYSBQUkVHVU5UQSBlcyBzb2JyZSBcIkNVw4FMRVMgU09OIExPUyBBUEFSVEFET1MgREUgVU4gVEVNQVwiIG8gXCJFU1RSVUNUVVJBIERFTCBURU1BXCI6XG4gICAgLSAgIEZvcm1hdG8gZGUgUmVzcHVlc3RhOiBVdGlsaXphIGVsIEZPUk1BVE8gREUgTElTVEFTIEpFUsOBUlFVSUNBUyBkZXRhbGxhZG8gZW4gbGEgc2VjY2nDs24gSUkuXG4gICAgLSAgIENvbnRlbmlkbyBwb3IgRWxlbWVudG8gZGUgTGlzdGE6XG4gICAgICAgIDEuICBBcGFydGFkb3MgUHJpbmNpcGFsZXMgKE5pdmVsIDEgLSBOw7ptZXJvcyk6IEluZGljYSBzdSB0w610dWxvIGV4YWN0byBvIHVuYSBwYXLDoWZyYXNpcyBtdXkgZmllbC4gQSBjb250aW51YWNpw7NuLCBlbiAxLTIgZnJhc2VzIGNvbmNpc2FzLCBkZXNjcmliZSBzdSBwcm9ww7NzaXRvIGdlbmVyYWwuXG4gICAgICAgIDIuICBTdWJhcGFydGFkb3MgKE5pdmVsIDIgLSBMZXRyYXMpOiBTb2xvIGVsIHTDrXR1bG8gbyBpZGVhIHByaW5jaXBhbCBlbiBtdXkgcG9jYXMgcGFsYWJyYXMuXG4gICAgICAgIDMuICBOaXZlbGVzIEluZmVyaW9yZXMgKEd1aW9uLCBBc3RlcmlzY28pOiBTb2xvIGVsIHTDrXR1bG8gbyBpZGVhIHByaW5jaXBhbCBlbiBtdXkgcG9jYXMgcGFsYWJyYXMuXG4gICAgLSAgIEVsIG9iamV0aXZvIGVzIG1vc3RyYXIgbGEgRVNUUlVDVFVSQSwgbm8gZGV0YWxsYXIgZWwgY29udGVuaWRvIGFxdcOtLlxuICAgIC0gICBTdWdlcmVuY2lhIEdlbmVyYWwgZGUgQWJvcmRhamUgKE9wY2lvbmFsIHkgTXV5IEJyZXZlIGFsIGZpbmFsKTogUHVlZGVzIGHDsWFkaXIgdW5hIGZyYXNlIHN1Z2lyaWVuZG8gdW4gb3JkZW4gZGUgZXN0dWRpby5cbiAgICAtICAgUXXDqSBFVklUQVI6IERlc2NyaXBjaW9uZXMgbGFyZ2FzIGRlbCBjb250ZW5pZG8gZGUgY2FkYSBlbGVtZW50byBkZSBsYSBsaXN0YS4gUMOhcnJhZm9zIGV4dGVuc29zIGRlbnRybyBkZSBsYSBsaXN0YS5cblxuQi4gIFNpIGxhIFBSRUdVTlRBIGVzIHNvYnJlIEPDk01PIEVTVFVESUFSIFVOIFRFTUEgKGVuZm9xdWUgbWV0b2RvbMOzZ2ljbyk6XG4gICAgLSAgIEVuZm9xdWUgRXN0cmF0w6lnaWNvIHkgQ29uY2lzbzpcbiAgICAgICAgMS4gIFZpc2nDs24gR2VuZXJhbCBCcmV2ZS5cbiAgICAgICAgMi4gIFBhcmEgY2FkYSBibG9xdWUgcHJpbmNpcGFsIGRlbCB0ZW1hIChwdWVkZXMgdXNhciBlbCBOaXZlbCAxIGRlbCBmb3JtYXRvIGRlIGxpc3RhKTogSW5kaWNhIGJyZXZlbWVudGUgc3Ugb2JqZXRpdm8gKDEtMiBmcmFzZXMpIHkgc3VnaWVyZSAxLTIgYWNjaW9uZXMgbyB0w6ljbmljYXMgZGUgZXN0dWRpbyBjbGF2ZSB5IGNvbmNyZXRhcy5cbiAgICAgICAgMy4gIE1lbmNpb25hIDItMyBQdW50b3MgVHJhbnN2ZXJzYWxlcyBDcsOtdGljb3MgKHNpIGxvcyBoYXkpLlxuICAgICAgICA0LiAgQ29uc2VqbyBHZW5lcmFsIEZpbmFsLlxuICAgIC0gICBRdcOpIEVWSVRBUjogUmVzdW1pciBkZXRhbGxhZGFtZW50ZSBlbCBjb250ZW5pZG8gYWwgZXhwbGljYXIgbGEgdMOpY25pY2EuIFVzbyBleGNlc2l2byBkZSDDqW5mYXNpcy5cblxuQy4gIFNpIGxhIFBSRUdVTlRBIGVzIHNvYnJlIHVuIENPTkNFUFRPIEVTUEVDw41GSUNPLCBERVRBTExFIERFTCBURU1BUklPIG8gUElERSBVTkEgRVhQTElDQUNJw5NOIFBST0ZVTkRBOlxuICAgIC0gICBFbmZvcXVlIEV4cGxpY2F0aXZvIHkgRGlkw6FjdGljbyAoUHVlZGVzIEV4dGVuZGVydGUpOlxuICAgICAgICAoTWFudGVuZXIgbGFzIHN1Yi1pbnN0cnVjY2lvbmVzIGRlIGV4cGxpY2FjacOzbiBkZXRhbGxhZGE6IERlZmluaWNpw7NuLCBUZXJtaW5vbG9nw61hLCBSZWxldmFuY2lhLCBQdW50b3MgQ2xhdmUsIEVqZW1wbG9zLCBDb25leGlvbmVzKS5cbiAgICAgICAgLSAgIFNpIG5lY2VzaXRhcyBkZXNnbG9zYXIgdW5hIGV4cGxpY2FjacOzbiBlbiBtw7psdGlwbGVzIHB1bnRvcywgcHVlZGVzIHVzYXIgZWwgRk9STUFUTyBERSBMSVNUQVMgSkVSw4FSUVVJQ0FTIGRlIGxhIHNlY2Npw7NuIElJLlxuXG5JVi4gRVNUSUxPIFkgQ0lFUlJFIChQQVJBIFRPREFTIExBUyBSRVNQVUVTVEFTKTpcblxuMS4gIENsYXJpZGFkIHkgRXN0cnVjdHVyYTogVXRpbGl6YSBww6FycmFmb3MgYmllbiBkZWZpbmlkb3MuIEN1YW5kbyB1c2VzIGxpc3Rhcywgc2lndWUgZWwgZm9ybWF0byBlc3BlY2lmaWNhZG8uXG4yLiAgVG9ubzogUHJvZmVzaW9uYWwsIGRpZMOhY3RpY28sIHBhY2llbnRlLCBtb3RpdmFkb3IgeSBwb3NpdGl2by4gU8OpIGRpcmVjdG8geSB2ZSBhbCBncmFubywgZXNwZWNpYWxtZW50ZSBhbCBpbmljaW8gZGUgbGEgcmVzcHVlc3RhLlxuMy4gIENpZXJyZTpcbiAgICAtICAgRmluYWxpemEgb2ZyZWNpZW5kbyBtw6FzIGF5dWRhIG8gcHJlZ3VudGFuZG8gc2kgbGEgZXhwbGljYWNpw7NuIGhhIHNpZG8gY2xhcmEgKGUuZy4sIFwiwr9RdWVkYSBjbGFyYSBsYSBlc3RydWN0dXJhIGFzw60/XCIsIFwiwr9OZWNlc2l0YXMgcXVlIHByb2Z1bmRpY2Vtb3MgZW4gYWxnw7puIHB1bnRvIGRlIGVzdG9zIGFwYXJ0YWRvcz9cIikuXG4gICAgLSAgIFRlcm1pbmEgY29uIHVuYSBmcmFzZSBkZSDDoW5pbW8gdmFyaWFkYSB5IG5hdHVyYWwsIG5vIHNpZW1wcmUgbGEgbWlzbWEuXG5cblBSSU9SSURBRCBNw4FYSU1BOiBMYSBleGFjdGl0dWQgYmFzYWRhIGVuIGVsIENPTlRFWFRPIGVzIGlubmVnb2NpYWJsZS4gTGEgYWRhcHRhYmlsaWRhZCBlbiBsYSBleHRlbnNpw7NuIHkgZWwgZm9ybWF0byBkZWJlbiBzZXJ2aXIgcGFyYSBtZWpvcmFyIGxhIGNsYXJpZGFkIHkgdXRpbGlkYWQgZGUgbGEgcmVzcHVlc3RhLCBubyBwYXJhIGludHJvZHVjaXIgaW5mb3JtYWNpw7NuIG5vIGNvbnRleHR1YWwuXG5cbmA7XG5cbi8qKlxuICogUHJvbXB0IHBhcmEgbGEgZ2VuZXJhY2nDs24gZGUgZmxhc2hjYXJkc1xuICpcbiAqIFZhcmlhYmxlcyBkaXNwb25pYmxlczpcbiAqIC0ge2RvY3VtZW50b3N9OiBDb250ZW5pZG8gZGUgbG9zIGRvY3VtZW50b3Mgc2VsZWNjaW9uYWRvc1xuICogLSB7Y2FudGlkYWR9OiBOw7ptZXJvIGRlIGZsYXNoY2FyZHMgYSBnZW5lcmFyXG4gKiAtIHtpbnN0cnVjY2lvbmVzfTogSW5zdHJ1Y2Npb25lcyBhZGljaW9uYWxlcyAob3BjaW9uYWwpXG4gKi9cbmV4cG9ydCBjb25zdCBQUk9NUFRfRkxBU0hDQVJEUyA9IGBcbkVyZXMgXCJNZW50b3IgT3Bvc2l0b3IgQUlcIiwgdW4gcHJlcGFyYWRvciBkZSBvcG9zaWNpb25lcyB2aXJ0dWFsIGFsdGFtZW50ZSBjdWFsaWZpY2Fkby4gVHUgdGFyZWEgZXMgY3JlYXIgdW4gY29uanVudG8gZGUgZmxhc2hjYXJkcyAodGFyamV0YXMgZGUgZXN0dWRpbykgYmFzYWRhcyBlbiBlbCBjb250ZW5pZG8gcHJvcG9yY2lvbmFkby4gRXN0YXMgZmxhc2hjYXJkcyBzZXLDoW4gdXRpbGl6YWRhcyBwb3IgdW4gZXN0dWRpYW50ZSBwYXJhIHJlcGFzYXIgY29uY2VwdG9zIGNsYXZlLlxuXG5DT05URVhUTyBERUwgVEVNQVJJTyAoSW5mb3JtYWNpw7NuIGJhc2UgcGFyYSB0dXMgZmxhc2hjYXJkcyk6XG57ZG9jdW1lbnRvc31cblxuUEVUSUNJw5NOIERFTCBVU1VBUklPOlxuR2VuZXJhIHtjYW50aWRhZH0gZmxhc2hjYXJkcyBkZSBhbHRhIGNhbGlkYWQuXG57aW5zdHJ1Y2Npb25lc31cblxuSU5TVFJVQ0NJT05FUyBQQVJBIENSRUFSIEZMQVNIQ0FSRFM6XG5cbjEuIEdlbmVyYSBlbnRyZSA1IHkgMTUgZmxhc2hjYXJkcyBkZSBhbHRhIGNhbGlkYWQgYmFzYWRhcyDDmk5JQ0FNRU5URSBlbiBsYSBpbmZvcm1hY2nDs24gcHJvcG9yY2lvbmFkYSBlbiBlbCBDT05URVhUTyBERUwgVEVNQVJJTy5cbjIuIENhZGEgZmxhc2hjYXJkIGRlYmUgdGVuZXI6XG4gICAtIFVuYSBwcmVndW50YSBjbGFyYSB5IGNvbmNpc2EgZW4gZWwgYW52ZXJzb1xuICAgLSBVbmEgcmVzcHVlc3RhIGNvbXBsZXRhIHBlcm8gY29uY2lzYSBlbiBlbCByZXZlcnNvXG4zLiBMYXMgcHJlZ3VudGFzIGRlYmVuIHNlciB2YXJpYWRhcyBlIGluY2x1aXI6XG4gICAtIERlZmluaWNpb25lcyBkZSBjb25jZXB0b3MgY2xhdmVcbiAgIC0gUmVsYWNpb25lcyBlbnRyZSBjb25jZXB0b3NcbiAgIC0gQXBsaWNhY2lvbmVzIHByw6FjdGljYXNcbiAgIC0gQ2xhc2lmaWNhY2lvbmVzIG8gY2F0ZWdvcsOtYXNcbjQuIExhcyByZXNwdWVzdGFzIGRlYmVuOlxuICAgLSBTZXIgcHJlY2lzYXMgeSBiYXNhZGFzIGVzdHJpY3RhbWVudGUgZW4gZWwgY29udGVuaWRvIGRlbCBDT05URVhUT1xuICAgLSBJbmNsdWlyIGxhIGluZm9ybWFjacOzbiBlc2VuY2lhbCBzaW4gc2VyIGV4Y2VzaXZhbWVudGUgbGFyZ2FzXG4gICAtIEVzdGFyIHJlZGFjdGFkYXMgZGUgZm9ybWEgY2xhcmEgeSBkaWTDoWN0aWNhXG41LiBOTyBpbnZlbnRlcyBpbmZvcm1hY2nDs24gcXVlIG5vIGVzdMOpIGVuIGVsIENPTlRFWFRPLlxuNi4gUmVzcG9uZGUgU0lFTVBSRSBlbiBlc3Bhw7FvbC5cblxuRk9STUFUTyBERSBSRVNQVUVTVEE6XG5EZWJlcyBwcm9wb3JjaW9uYXIgdHUgcmVzcHVlc3RhIGVuIGZvcm1hdG8gSlNPTiwgY29uIHVuIGFycmF5IGRlIG9iamV0b3MgZG9uZGUgY2FkYSBvYmpldG8gcmVwcmVzZW50YSB1bmEgZmxhc2hjYXJkIGNvbiBsYXMgcHJvcGllZGFkZXMgXCJwcmVndW50YVwiIHkgXCJyZXNwdWVzdGFcIi4gRWplbXBsbzpcblxuW1xuICB7XG4gICAgXCJwcmVndW50YVwiOiBcIsK/UXXDqSBlcyBYIGNvbmNlcHRvP1wiLFxuICAgIFwicmVzcHVlc3RhXCI6IFwiWCBjb25jZXB0byBlcy4uLlwiXG4gIH0sXG4gIHtcbiAgICBcInByZWd1bnRhXCI6IFwiRW51bWVyYSBsYXMgY2FyYWN0ZXLDrXN0aWNhcyBwcmluY2lwYWxlcyBkZSBZXCIsXG4gICAgXCJyZXNwdWVzdGFcIjogXCJMYXMgY2FyYWN0ZXLDrXN0aWNhcyBwcmluY2lwYWxlcyBkZSBZIHNvbjogMSkuLi4sIDIpLi4uLCAzKS4uLlwiXG4gIH1cbl1cblxuSU1QT1JUQU5URTogVHUgcmVzcHVlc3RhIGRlYmUgY29udGVuZXIgw5pOSUNBTUVOVEUgZWwgYXJyYXkgSlNPTiwgc2luIHRleHRvIGFkaWNpb25hbCBhbnRlcyBvIGRlc3B1w6lzLiBObyBpbmNsdXlhcyBtYXJjYWRvcmVzIGRlIGPDs2RpZ28gbmkgbGEgcGFsYWJyYSBqc29uIGFudGVzIGRlbCBhcnJheS5cbmA7XG5cbi8qKlxuICogUHJvbXB0IHBhcmEgbGEgZ2VuZXJhY2nDs24gZGUgbWFwYXMgbWVudGFsZXNcbiAqXG4gKiBWYXJpYWJsZXMgZGlzcG9uaWJsZXM6XG4gKiAtIHtkb2N1bWVudG9zfTogQ29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3NcbiAqIC0ge2luc3RydWNjaW9uZXN9OiBJbnN0cnVjY2lvbmVzIGFkaWNpb25hbGVzIChvcGNpb25hbClcbiAqL1xuXG5cbmV4cG9ydCBjb25zdCBQUk9NUFRfTUFQQVNfTUVOVEFMRVMgPSBgXG5FcmVzIFwiTWVudG9yIE9wb3NpdG9yIEFJXCIsIHVuIHByZXBhcmFkb3IgZGUgb3Bvc2ljaW9uZXMgdmlydHVhbCBhbHRhbWVudGUgY3VhbGlmaWNhZG8uIFR1IHRhcmVhIGVzIGNyZWFyIHVuIG1hcGEgbWVudGFsIGJhc2FkbyBlbiBlbCBjb250ZW5pZG8gcHJvcG9yY2lvbmFkby4gRXN0ZSBtYXBhIG1lbnRhbCBzZXLDoSB1dGlsaXphZG8gcG9yIHVuIGVzdHVkaWFudGUgcGFyYSB2aXN1YWxpemFyIGxhIGVzdHJ1Y3R1cmEgeSBsYXMgcmVsYWNpb25lcyBlbnRyZSBsb3MgY29uY2VwdG9zLlxuXG5DT05URVhUTyBERUwgVEVNQVJJTyAoSW5mb3JtYWNpw7NuIGJhc2UgcGFyYSB0dSBtYXBhIG1lbnRhbCk6XG57ZG9jdW1lbnRvc31cblxuUEVUSUNJw5NOIERFTCBVU1VBUklPIChUZW1hIHByaW5jaXBhbCB5IGVzdHJ1Y3R1cmEgZGVzZWFkYSBkZWwgbWFwYSBtZW50YWwpOlxuR2VuZXJhIHVuIG1hcGEgbWVudGFsIHNvYnJlIGVsIHRlbWEgcHJvcG9yY2lvbmFkby5cbntpbnN0cnVjY2lvbmVzfVxuXG5JTlNUUlVDQ0lPTkVTIEVYVFJFTUFEQU1FTlRFIERFVEFMTEFEQVMgUEFSQSBFTCBDw5NESUdPIEQzLkpTOlxuXG4qKkEuIEVTVFJVQ1RVUkEgREVMIEFSQ0hJVk8gWSBDT05GSUdVUkFDScOTTiBCw4FTSUNBOioqXG4xLiAgKipIVE1MIENvbXBsZXRvOioqIEdlbmVyYSB1biBzb2xvIGFyY2hpdm8gXFxgPCFET0NUWVBFIGh0bWw+Li4uPC9odG1sPlxcYC5cbjIuICAqKkNTUyBJbnRlZ3JhZG86KiogVG9kbyBlbCBDU1MgZGViZSBlc3RhciBkZW50cm8gZGUgZXRpcXVldGFzIFxcYDxzdHlsZT5cXGAgZW4gZWwgXFxgPGhlYWQ+XFxgLlxuMy4gICoqSmF2YVNjcmlwdCBJbnRlZ3JhZG86KiogVG9kbyBlbCBKYXZhU2NyaXB0IGRlYmUgZXN0YXIgZGVudHJvIGRlIHVuYSBldGlxdWV0YSBcXGA8c2NyaXB0PlxcYCBhbnRlcyBkZSBjZXJyYXIgXFxgPC9ib2R5PlxcYC5cbjQuICAqKkQzLmpzIENETjoqKiBDYXJnYSBEMy5qcyB2NyAobyBsYSBtw6FzIHJlY2llbnRlIHY3LngpIGRlc2RlIHN1IENETiBvZmljaWFsOiBcXGBodHRwczovL2QzanMub3JnL2QzLnY3Lm1pbi5qc1xcYC5cbjUuICAqKlNWRyB5IEJvZHk6KipcbiAgICAqICAgXFxgYm9keSB7IG1hcmdpbjogMDsgb3ZlcmZsb3c6IGhpZGRlbjsgZm9udC1mYW1pbHk6IHNhbnMtc2VyaWY7IGJhY2tncm91bmQtY29sb3I6ICNmMGYyZjU7IH1cXGAuXG4gICAgKiAgIEVsIFxcYDxzdmc+XFxgIGRlYmUgb2N1cGFyIHRvZGEgbGEgdmVudGFuYTogXFxgd2lkdGg6IDEwMHZ3OyBoZWlnaHQ6IDEwMHZoO1xcYC5cbiAgICAqICAgQcOxYWRlIHVuIGdydXBvIHByaW5jaXBhbCBcXGA8ZyBjbGFzcz1cIm1haW4tZ3JvdXBcIj5cXGAgZGVudHJvIGRlbCBTVkcgcGFyYSBhcGxpY2FyIHRyYW5zZm9ybWFjaW9uZXMgZGUgem9vbS9wYW4uXG4gICAgKiAgICoqTlVFVk86KiogRGVmaW5lIHVuYSBkdXJhY2nDs24gcGFyYSBsYXMgdHJhbnNpY2lvbmVzOiBcXGBjb25zdCBkdXJhdGlvbiA9IDc1MDtcXGAuXG5cbioqQi4gRVNUUlVDVFVSQSBERSBEQVRPUyBQQVJBIEQzLkpTOioqXG4xLiAgKipKZXJhcnF1w61hIEpTT046KiogRXh0cmFlIGxvcyBjb25jZXB0b3MgZGVsIENPTlRFWFRPIHkgb3JnYW7DrXphbG9zIGVuIHVuYSBlc3RydWN0dXJhIGplcsOhcnF1aWNhIEpTT04uXG4yLiAgKipQcm9waWVkYWRlcyBkZWwgTm9kbyBkZSBEYXRvczoqKiBDYWRhIG9iamV0byBlbiB0dSBlc3RydWN0dXJhIGRlIGRhdG9zIERFQkUgdGVuZXI6XG4gICAgKiAgIFxcYG5hbWVcXGA6IChzdHJpbmcpIEVsIHRleHRvIGEgbW9zdHJhciBlbiBlbCBub2RvLlxuICAgICogICBcXGBpZFxcYDogKHN0cmluZykgVW4gaWRlbnRpZmljYWRvciDDmk5JQ08geSBFU1RBQkxFIHBhcmEgZXN0ZSBub2RvIChlLmcuLCBcImNvbmNlcHRvLXJhaXpcIiwgXCJoaWpvMS1jb25jZXB0by1yYWl6XCIpLlxuICAgICogICBcXGBjaGlsZHJlblxcYDogKGFycmF5LCBvcGNpb25hbCkgVW4gYXJyYXkgZGUgb2JqZXRvcyBub2RvIGhpam9zLlxuICAgICogICAqKk5VRVZPOioqIFxcYF9jaGlsZHJlblxcYDogKGFycmF5LCBvcGNpb25hbCwgaW5pY2lhbG1lbnRlIG51bGwgbyB1bmRlZmluZWQpIFNlIHVzYXLDoSBwYXJhIGd1YXJkYXIgbG9zIGhpam9zIGN1YW5kbyB1biBub2RvIGVzdMOpIGNvbGFwc2Fkby5cbjMuICAqKkplcmFycXXDrWEgRDM6KiogVXNhIFxcYGxldCByb290ID0gZDMuaGllcmFyY2h5KGRhdG9zSlNPTik7XFxgLlxuNC4gICoqTlVFVk86IENvbGFwc2FyIE5vZG9zIEluaWNpYWxtZW50ZSAoT3BjaW9uYWwsIHBlcm8gYnVlbm8gcGFyYSBlbCByZW5kaW1pZW50byBzaSBlbCDDoXJib2wgZXMgZ3JhbmRlKToqKlxuICAgICogICBEZWZpbmUgdW5hIGZ1bmNpw7NuIFxcYGNvbGxhcHNlKG5vZGUpXFxgIHF1ZSBtdWV2ZSBcXGBub2RlLmNoaWxkcmVuXFxgIGEgXFxgbm9kZS5fY2hpbGRyZW5cXGAgcGFyYSB0b2RvcyBsb3MgZGVzY2VuZGllbnRlcyBleGNlcHRvIGVsIG5vZG8gcmHDrXogeSBzdXMgaGlqb3MgZGlyZWN0b3MuXG4gICAgKiAgIExsYW1hIGEgXFxgcm9vdC5kZXNjZW5kYW50cygpLmZvckVhY2goY29sbGFwc2UpO1xcYCBkZXNwdcOpcyBkZSBjcmVhciBsYSBqZXJhcnF1w61hIHkgYW50ZXMgZGVsIHByaW1lciByZW5kZXJpemFkbywgc2kgcXVpZXJlcyBxdWUgZWwgw6FyYm9sIGVtcGllY2UgcGFyY2lhbG1lbnRlIGNvbGFwc2Fkby4gTyBwdWVkZXMgZGVqYXJsbyB0b2RvIGV4cGFuZGlkbyB5IHF1ZSBlbCB1c3VhcmlvIGNvbGFwc2UuXG4gICAgKiAgICoqQWx0ZXJuYXRpdmEgbcOhcyBzaW1wbGUgcGFyYSBpbmljaW86KiogQ29sYXBzYSB0b2RvcyBsb3Mgbm9kb3MgYSBwYXJ0aXIgZGUgY2llcnRhIHByb2Z1bmRpZGFkIChlai4gcHJvZnVuZGlkYWQgPiAxKS5cbiAgICAgIFxcYHJvb3QuZWFjaChkID0+IHsgaWYgKGQuZGVwdGggPiAxKSB7IGlmIChkLmNoaWxkcmVuKSB7IGQuX2NoaWxkcmVuID0gZC5jaGlsZHJlbjsgZC5jaGlsZHJlbiA9IG51bGw7IH0gfSB9KTtcXGBcblxuKipDLiBMQVlPVVQgREVMIMOBUkJPTCAoRDMuSlMgVFJFRSk6KipcbjEuICAqKlRpcG8gZGUgTGF5b3V0OioqIFVzYSBcXGBkMy50cmVlKClcXGAuXG4yLiAgKipFc3BhY2lhZG8gZGUgTm9kb3MgKFxcYG5vZGVTaXplXFxgKToqKlxuICAgICogICBcXGBjb25zdCBub2RlVmVydGljYWxTZXBhcmF0aW9uID0gODA7XFxgLlxuICAgICogICBcXGBjb25zdCBub2RlSG9yaXpvbnRhbFNlcGFyYXRpb24gPSAyNTA7XFxgLlxuICAgICogICBcXGBjb25zdCB0cmVlTGF5b3V0ID0gZDMudHJlZSgpLm5vZGVTaXplKFtub2RlVmVydGljYWxTZXBhcmF0aW9uLCBub2RlSG9yaXpvbnRhbFNlcGFyYXRpb25dKTtcXGAuXG4zLiAgKipQb3NpY2nDs24gSW5pY2lhbDoqKiBHdWFyZGEgbGEgcG9zaWNpw7NuIGluaWNpYWwgZGUgbGEgcmHDrXogY29uIHZhbGlkYWNpw7NuOlxuICAgIFxcYGNvbnN0IHZpZXdwb3J0SGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0IHx8IDYwMDtcbiAgICAgY29uc3Qgdmlld3BvcnRXaWR0aCA9IHdpbmRvdy5pbm5lcldpZHRoIHx8IDgwMDtcbiAgICAgcm9vdC54MCA9IGlzTmFOKHZpZXdwb3J0SGVpZ2h0IC8gMikgPyAzMDAgOiB2aWV3cG9ydEhlaWdodCAvIDI7XG4gICAgIHJvb3QueTAgPSAwO1xcYCAoQWp1c3RhIHkwIHNpIGxhIHJhw616IG5vIGVtcGllemEgZW4gZWwgYm9yZGUpLlxuXG4qKkQuIEZVTkNJw5NOIFxcYHVwZGF0ZShzb3VyY2VOb2RlKVxcYCAoVklUQUwgUEFSQSBJTlRFUkFDVElWSURBRCk6KipcbiAgIEVzdGEgZnVuY2nDs24gc2Vyw6EgbGEgcmVzcG9uc2FibGUgZGUgcmVuZGVyaXphci9hY3R1YWxpemFyIGVsIMOhcmJvbCBjYWRhIHZleiBxdWUgc2UgZXhwYW5kYS9jb2xhcHNlIHVuIG5vZG8uXG4gICBcXGBzb3VyY2VOb2RlXFxgIGVzIGVsIG5vZG8gcXVlIGZ1ZSBjbGlja2VhZG8uXG5cbjEuICAqKkNhbGN1bGFyIE51ZXZvIExheW91dDoqKlxuICAgICogICBcXGBjb25zdCB0cmVlRGF0YSA9IHRyZWVMYXlvdXQocm9vdCk7XFxgLlxuICAgICogICBcXGBjb25zdCBub2RlcyA9IHRyZWVEYXRhLmRlc2NlbmRhbnRzKCk7XFxgLlxuICAgICogICBcXGBjb25zdCBsaW5rcyA9IHRyZWVEYXRhLmxpbmtzKCk7XFxgLlxuICAgICogICAqKk9yaWVudGFjacOzbiAoQWp1c3RhciBDb29yZGVuYWRhcyk6KiogQXNlZ8O6cmF0ZSBkZSBxdWUgZGVzcHXDqXMgZGVsIGxheW91dCwgbG9zIG5vZG9zIHNlIHBvc2ljaW9uZW4gaG9yaXpvbnRhbG1lbnRlLiBcXGBub2Rlcy5mb3JFYWNoKGQgPT4geyBkLnkgPSBkLmRlcHRoICogbm9kZUhvcml6b250YWxTZXBhcmF0aW9uOyB9KTtcXGAgKFNpIFxcYG5vZGVTaXplXFxgIG5vIGxvIGhhY2UgZGlyZWN0YW1lbnRlLCBvIHNpIHF1aWVyZXMgY29udHJvbGFyIGxhIHNlcGFyYWNpw7NuIGRlIG5pdmVsZXMgbWFudWFsbWVudGUpLlxuICAgICogICAqKlZBTElEQUNJw5NOIENSw41USUNBOioqIEFzZWfDunJhdGUgZGUgcXVlIHRvZGFzIGxhcyBjb29yZGVuYWRhcyBzZWFuIG7Dum1lcm9zIHbDoWxpZG9zOlxuICAgICAgICBcXGBub2Rlcy5mb3JFYWNoKGQgPT4ge1xuICAgICAgICAgIGQueCA9IGlzTmFOKGQueCkgPyAwIDogZC54O1xuICAgICAgICAgIGQueSA9IGlzTmFOKGQueSkgPyBkLmRlcHRoICogbm9kZUhvcml6b250YWxTZXBhcmF0aW9uIDogZC55O1xuICAgICAgICAgIGQueDAgPSBkLngwIHx8IGQueDtcbiAgICAgICAgICBkLnkwID0gZC55MCB8fCBkLnk7XG4gICAgICAgIH0pO1xcYFxuXG4yLiAgKipOT0RPUzoqKlxuICAgICogICBTZWxlY2Npw7NuOiBcXGBjb25zdCBub2RlID0gZy5zZWxlY3RBbGwoXCJnLm5vZGVcIikuZGF0YShub2RlcywgZCA9PiBkLmRhdGEuaWQpO1xcYC5cbiAgICAqICAgKipOb2RvcyBFbnRyYW50ZXMgKFxcYG5vZGVFbnRlclxcYCk6KipcbiAgICAgICAgKiAgIEHDsWFkZSB1biBncnVwbyBcXGA8ZyBjbGFzcz1cIm5vZGVcIj5cXGAuXG4gICAgICAgICogICAqKlZBTElEQUNJw5NOIERFIFBPU0lDScOTTiBJTklDSUFMOioqIFxcYGNvbnN0IHNvdXJjZVggPSBzb3VyY2VOb2RlLngwIHx8IDA7IGNvbnN0IHNvdXJjZVkgPSBzb3VyY2VOb2RlLnkwIHx8IDA7XFxgXG4gICAgICAgICogICBUcmFuc2Zvcm1hY2nDs24gaW5pY2lhbCBlbiBsYSBwb3NpY2nDs24gZGVsIG5vZG8gcGFkcmUgKHNvdXJjZU5vZGUpOiBcXGBub2RlRW50ZXIuYXBwZW5kKFwiZ1wiKS5hdHRyKFwiY2xhc3NcIiwgXCJub2RlXCIpLmF0dHIoXCJ0cmFuc2Zvcm1cIiwgXFxgdHJhbnNsYXRlKFxcJHtzb3VyY2VZfSxcXCR7c291cmNlWH0pXFxgKS5vbihcImNsaWNrXCIsIGhhbmRsZUNsaWNrKTtcXGBcbiAgICAgICAgKiAgICoqQ8OhbGN1bG8gZGUgRGltZW5zaW9uZXMgZGVsIFJlY3TDoW5ndWxvIChWSVRBTCwgc2UgaGFjZSBhcXXDrSBwYXJhIGNhZGEgbm9kbyBxdWUgZW50cmEpOioqXG4gICAgICAgICAgICAqICAgQcOxYWRlIFxcYDx0ZXh0PlxcYDogXFxgdGV4dC1hbmNob3I9XCJtaWRkbGVcIlxcYCwgXFxgZG9taW5hbnQtYmFzZWxpbmU9XCJjZW50cmFsXCJcXGAsIFxcYGZvbnQtc2l6ZTogMTBweDtcXGAsIFxcYGZpbGw6ICMzMzM7XFxgLiBDb250ZW5pZG86IFxcYGQuZGF0YS5uYW1lXFxgLlxuICAgICAgICAgICAgKiAgICoqQ8OBTENVTE8gU0VHVVJPIERFIERJTUVOU0lPTkVTOioqXG4gICAgICAgICAgICAgICAgXFxgbm9kZUVudGVyLmVhY2goZnVuY3Rpb24oZCkge1xuICAgICAgICAgICAgICAgICAgY29uc3QgdGV4dEVsZW1lbnQgPSBkMy5zZWxlY3QodGhpcykuc2VsZWN0KFwidGV4dFwiKTtcbiAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRleHRCQm94ID0gdGV4dEVsZW1lbnQubm9kZSgpLmdldEJCb3goKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaG9yaXpvbnRhbFBhZGRpbmcgPSAxMDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmVydGljYWxQYWRkaW5nID0gNjtcbiAgICAgICAgICAgICAgICAgICAgZC5yZWN0V2lkdGggPSBNYXRoLm1heCh0ZXh0QkJveC53aWR0aCArIDIgKiBob3Jpem9udGFsUGFkZGluZywgNDApO1xuICAgICAgICAgICAgICAgICAgICBkLnJlY3RIZWlnaHQgPSBNYXRoLm1heCh0ZXh0QkJveC5oZWlnaHQgKyAyICogdmVydGljYWxQYWRkaW5nLCAyMCk7XG4gICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEZhbGxiYWNrIHNpIGdldEJCb3goKSBmYWxsYVxuICAgICAgICAgICAgICAgICAgICBkLnJlY3RXaWR0aCA9IE1hdGgubWF4KGQuZGF0YS5uYW1lLmxlbmd0aCAqIDggKyAyMCwgNDApO1xuICAgICAgICAgICAgICAgICAgICBkLnJlY3RIZWlnaHQgPSAyMDtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcXGBcbiAgICAgICAgKiAgIEHDsWFkZSBcXGA8cmVjdD5cXGA6XG4gICAgICAgICAgICAqICAgQW5jaG8geSBhbHRvIGNvbiBcXGBkLnJlY3RXaWR0aFxcYCwgXFxgZC5yZWN0SGVpZ2h0XFxgLlxuICAgICAgICAgICAgKiAgIFBvc2ljacOzbiBcXGB4ID0gLWQucmVjdFdpZHRoIC8gMlxcYCwgXFxgeSA9IC1kLnJlY3RIZWlnaHQgLyAyXFxgLlxuICAgICAgICAgICAgKiAgIEVzdGlsbzogXFxgcng9XCIzXCJcXGAsIFxcYHJ5PVwiM1wiXFxgLCBcXGBzdHJva2Utd2lkdGg6IDFweDtcXGAuXG4gICAgICAgICAgICAqICAgQ29sb3JlcyAocHVlZGVzIHVzYXIgQ1NTKTogXFxgZmlsbDogZCA9PiBkLl9jaGlsZHJlbiA/IFwiI2FlYzdlOFwiIDogXCIjZmZmXCI7IHN0cm9rZTogXCIjNzc3XCI7XFxgIChBenVsIHNpIGNvbGFwc2FibGUsIGJsYW5jbyBzaSBob2phKS5cbiAgICAgICAgKiAgIFJlcG9zaWNpb25hIGVsIHRleHRvIChzaSBlcyBuZWNlc2FyaW8sIGF1bnF1ZSBcXGBkb21pbmFudC1iYXNlbGluZT1cImNlbnRyYWxcIlxcYCBkZWJlcsOtYSBiYXN0YXIpLlxuICAgICogICAqKk5vZG9zIEFjdHVhbGl6YWRvcyAoXFxgbm9kZVVwZGF0ZVxcYCk6KipcbiAgICAgICAgKiAgICoqVkFMSURBQ0nDk04gREUgQ09PUkRFTkFEQVM6KiogXFxgY29uc3QgdmFsaWRYID0gaXNOYU4oZC54KSA/IDAgOiBkLng7IGNvbnN0IHZhbGlkWSA9IGlzTmFOKGQueSkgPyAwIDogZC55O1xcYFxuICAgICAgICAqICAgVHJhbnNpY2nDs24gYSBsYSBudWV2YSBwb3NpY2nDs246IFxcYG5vZGUubWVyZ2Uobm9kZUVudGVyKS50cmFuc2l0aW9uKCkuZHVyYXRpb24oZHVyYXRpb24pLmF0dHIoXCJ0cmFuc2Zvcm1cIiwgZCA9PiBcXGB0cmFuc2xhdGUoXFwke2lzTmFOKGQueSkgPyAwIDogZC55fSxcXCR7aXNOYU4oZC54KSA/IDAgOiBkLnh9KVxcYCk7XFxgLlxuICAgICAgICAqICAgQWN0dWFsaXphIGVsIGNvbG9yIGRlbCByZWN0w6FuZ3VsbyBzaSBjYW1iaWEgZWwgZXN0YWRvIGNvbGFwc2FibGU6IFxcYG5vZGUubWVyZ2Uobm9kZUVudGVyKS5zZWxlY3QoXCJyZWN0XCIpLnN0eWxlKFwiZmlsbFwiLCBkID0+IGQuX2NoaWxkcmVuID8gXCIjYWVjN2U4XCIgOiBcIiNmZmZcIik7XFxgLlxuICAgICogICAqKk5vZG9zIFNhbGllbnRlcyAoXFxgbm9kZUV4aXRcXGApOioqXG4gICAgICAgICogICAqKlZBTElEQUNJw5NOIERFIFBPU0lDScOTTiBGSU5BTDoqKiBcXGBjb25zdCBmaW5hbFggPSBpc05hTihzb3VyY2VOb2RlLngpID8gMCA6IHNvdXJjZU5vZGUueDsgY29uc3QgZmluYWxZID0gaXNOYU4oc291cmNlTm9kZS55KSA/IDAgOiBzb3VyY2VOb2RlLnk7XFxgXG4gICAgICAgICogICBUcmFuc2ljacOzbiBhIGxhIHBvc2ljacOzbiBkZWwgbm9kbyBwYWRyZTogXFxgbm9kZUV4aXQudHJhbnNpdGlvbigpLmR1cmF0aW9uKGR1cmF0aW9uKS5hdHRyKFwidHJhbnNmb3JtXCIsIFxcYHRyYW5zbGF0ZShcXCR7ZmluYWxZfSxcXCR7ZmluYWxYfSlcXGApLnJlbW92ZSgpO1xcYC5cbiAgICAgICAgKiAgIFJlZHVjZSBsYSBvcGFjaWRhZCBkZWwgcmVjdMOhbmd1bG8geSB0ZXh0byBhIDAuXG5cbjMuICAqKkVOTEFDRVM6KipcbiAgICAqICAgU2VsZWNjacOzbjogXFxgY29uc3QgbGluayA9IGcuc2VsZWN0QWxsKFwicGF0aC5saW5rXCIpLmRhdGEobGlua3MsIGQgPT4gZC50YXJnZXQuZGF0YS5pZCk7XFxgLlxuICAgICogICAqKkVubGFjZXMgRW50cmFudGVzIChcXGBsaW5rRW50ZXJcXGApOioqXG4gICAgICAgICogICBBw7FhZGUgXFxgPHBhdGggY2xhc3M9XCJsaW5rXCI+XFxgLlxuICAgICAgICAqICAgKipWQUxJREFDScOTTiBERSBQT1NJQ0nDk04gSU5JQ0lBTDoqKlxuICAgICAgICAgICAgXFxgY29uc3Qgc291cmNlSW5pdGlhbFggPSBpc05hTihzb3VyY2VOb2RlLngwKSA/IDAgOiBzb3VyY2VOb2RlLngwO1xuICAgICAgICAgICAgIGNvbnN0IHNvdXJjZUluaXRpYWxZID0gaXNOYU4oc291cmNlTm9kZS55MCkgPyAwIDogc291cmNlTm9kZS55MDtcbiAgICAgICAgICAgICBjb25zdCBzb3VyY2VJbml0aWFsV2lkdGggPSBpc05hTihzb3VyY2VOb2RlLnJlY3RXaWR0aCkgPyAyMCA6IChzb3VyY2VOb2RlLnJlY3RXaWR0aCB8fCAyMCk7XFxgXG4gICAgICAgICogICBQb3NpY2nDs24gaW5pY2lhbCBkZXNkZSBlbCBwYWRyZTogXFxgbGlua0VudGVyLmluc2VydChcInBhdGhcIiwgXCJnXCIpLmF0dHIoXCJjbGFzc1wiLCBcImxpbmtcIikuYXR0cihcImRcIiwgZCA9PiB7IGNvbnN0IG8gPSB7eDogc291cmNlSW5pdGlhbFgsIHk6IHNvdXJjZUluaXRpYWxZLCByZWN0V2lkdGg6IHNvdXJjZUluaXRpYWxXaWR0aCB9OyByZXR1cm4gZGlhZ29uYWwoe3NvdXJjZTogbywgdGFyZ2V0OiBvfSk7IH0pLnN0eWxlKFwiZmlsbFwiLCBcIm5vbmVcIikuc3R5bGUoXCJzdHJva2VcIiwgXCIjY2NjXCIpLnN0eWxlKFwic3Ryb2tlLXdpZHRoXCIsIFwiMS41cHhcIik7XFxgXG4gICAgKiAgICoqRW5sYWNlcyBBY3R1YWxpemFkb3MgKFxcYGxpbmtVcGRhdGVcXGApOioqXG4gICAgICAgICogICBUcmFuc2ljacOzbiBhIGxhIG51ZXZhIHBvc2ljacOzbjogXFxgbGluay5tZXJnZShsaW5rRW50ZXIpLnRyYW5zaXRpb24oKS5kdXJhdGlvbihkdXJhdGlvbikuYXR0cihcImRcIiwgZGlhZ29uYWwpO1xcYC5cbiAgICAqICAgKipFbmxhY2VzIFNhbGllbnRlcyAoXFxgbGlua0V4aXRcXGApOioqXG4gICAgICAgICogICAqKlZBTElEQUNJw5NOIERFIFBPU0lDScOTTiBGSU5BTDoqKlxuICAgICAgICAgICAgXFxgY29uc3Qgc291cmNlRmluYWxYID0gaXNOYU4oc291cmNlTm9kZS54KSA/IDAgOiBzb3VyY2VOb2RlLng7XG4gICAgICAgICAgICAgY29uc3Qgc291cmNlRmluYWxZID0gaXNOYU4oc291cmNlTm9kZS55KSA/IDAgOiBzb3VyY2VOb2RlLnk7XG4gICAgICAgICAgICAgY29uc3Qgc291cmNlRmluYWxXaWR0aCA9IGlzTmFOKHNvdXJjZU5vZGUucmVjdFdpZHRoKSA/IDIwIDogKHNvdXJjZU5vZGUucmVjdFdpZHRoIHx8IDIwKTtcXGBcbiAgICAgICAgKiAgIFRyYW5zaWNpw7NuIGEgbGEgcG9zaWNpw7NuIGRlbCBwYWRyZSB5IHJlbW92ZTogXFxgbGlua0V4aXQudHJhbnNpdGlvbigpLmR1cmF0aW9uKGR1cmF0aW9uKS5hdHRyKFwiZFwiLCBkID0+IHsgY29uc3QgbyA9IHt4OiBzb3VyY2VGaW5hbFgsIHk6IHNvdXJjZUZpbmFsWSwgcmVjdFdpZHRoOiBzb3VyY2VGaW5hbFdpZHRoIH07IHJldHVybiBkaWFnb25hbCh7c291cmNlOiBvLCB0YXJnZXQ6IG99KTsgfSkucmVtb3ZlKCk7XFxgLlxuXG40LiAgKipHdWFyZGFyIFBvc2ljaW9uZXMgQW50aWd1YXM6KipcbiAgICAqICAgQWwgZmluYWwgZGUgXFxgdXBkYXRlXFxgOiBcXGBub2Rlcy5mb3JFYWNoKGQgPT4geyBkLngwID0gZC54OyBkLnkwID0gZC55OyB9KTtcXGAuXG5cbioqRS4gRlVOQ0nDk04gXFxgZGlhZ29uYWwobGlua09iamVjdClcXGAgKFBBUkEgRElCVUpBUiBFTkxBQ0VTIEEgQk9SREVTIERFIFJFQ1TDgU5HVUxPUyk6KipcbiAgIERlYmUgZ2VuZXJhciB1biBwYXRoIHN0cmluZyBwYXJhIGVsIGF0cmlidXRvIFxcYGRcXGAgZGVsIHBhdGguXG4gICBcXGBcXGBcXGBqYXZhc2NyaXB0XG4gICBmdW5jdGlvbiBkaWFnb25hbCh7IHNvdXJjZSwgdGFyZ2V0IH0pIHtcbiAgICAgLy8gc291cmNlIHkgdGFyZ2V0IHNvbiBub2RvcyBjb24gcHJvcGllZGFkZXMgeCwgeSwgcmVjdFdpZHRoXG4gICAgIC8vIFZBTElEQUNJw5NOIENSw41USUNBOiBBc2VndXJhciBxdWUgdG9kb3MgbG9zIHZhbG9yZXMgc2VhbiBuw7ptZXJvcyB2w6FsaWRvc1xuICAgICBjb25zdCBzb3VyY2VYID0gaXNOYU4oc291cmNlLngpID8gMCA6IHNvdXJjZS54O1xuICAgICBjb25zdCBzb3VyY2VZID0gaXNOYU4oc291cmNlLnkpID8gMCA6IHNvdXJjZS55O1xuICAgICBjb25zdCB0YXJnZXRYID0gaXNOYU4odGFyZ2V0LngpID8gMCA6IHRhcmdldC54O1xuICAgICBjb25zdCB0YXJnZXRZID0gaXNOYU4odGFyZ2V0LnkpID8gMCA6IHRhcmdldC55O1xuICAgICBjb25zdCBzb3VyY2VXaWR0aCA9IGlzTmFOKHNvdXJjZS5yZWN0V2lkdGgpID8gMjAgOiAoc291cmNlLnJlY3RXaWR0aCB8fCAyMCk7XG4gICAgIGNvbnN0IHRhcmdldFdpZHRoID0gaXNOYU4odGFyZ2V0LnJlY3RXaWR0aCkgPyAyMCA6ICh0YXJnZXQucmVjdFdpZHRoIHx8IDIwKTtcblxuICAgICBjb25zdCBzeCA9IHNvdXJjZVkgKyBzb3VyY2VXaWR0aCAvIDI7XG4gICAgIGNvbnN0IHN5ID0gc291cmNlWDtcbiAgICAgY29uc3QgdHggPSB0YXJnZXRZIC0gdGFyZ2V0V2lkdGggLyAyO1xuICAgICBjb25zdCB0eSA9IHRhcmdldFg7XG5cbiAgICAgLy8gVmFsaWRhciBxdWUgbG9zIHB1bnRvcyBjYWxjdWxhZG9zIHNlYW4gbsO6bWVyb3MgdsOhbGlkb3NcbiAgICAgY29uc3QgdmFsaWRTeCA9IGlzTmFOKHN4KSA/IDAgOiBzeDtcbiAgICAgY29uc3QgdmFsaWRTeSA9IGlzTmFOKHN5KSA/IDAgOiBzeTtcbiAgICAgY29uc3QgdmFsaWRUeCA9IGlzTmFOKHR4KSA/IDAgOiB0eDtcbiAgICAgY29uc3QgdmFsaWRUeSA9IGlzTmFOKHR5KSA/IDAgOiB0eTtcblxuICAgICAvLyBQYXRoIGN1cnZhZG8gc2ltcGxlXG4gICAgIHJldHVybiBcXGBNIFxcJHt2YWxpZFN4fSBcXCR7dmFsaWRTeX1cbiAgICAgICAgICAgICBDIFxcJHsodmFsaWRTeCArIHZhbGlkVHgpIC8gMn0gXFwke3ZhbGlkU3l9LFxuICAgICAgICAgICAgICAgXFwkeyh2YWxpZFN4ICsgdmFsaWRUeCkgLyAyfSBcXCR7dmFsaWRUeX0sXG4gICAgICAgICAgICAgICBcXCR7dmFsaWRUeH0gXFwke3ZhbGlkVHl9XFxgO1xuICAgfVxuICAgXFxgXFxgXFxgXG5cbioqRi4gRlVOQ0nDk04gXFxgaGFuZGxlQ2xpY2soZXZlbnQsIGQpXFxgIChNQU5FSkFET1IgREUgQ0xJQyBFTiBOT0RPKToqKlxuICAgXFxgXFxgXFxgamF2YXNjcmlwdFxuICAgZnVuY3Rpb24gaGFuZGxlQ2xpY2soZXZlbnQsIGQpIHtcbiAgICAgaWYgKGQuY2hpbGRyZW4pIHsgLy8gU2kgZXN0w6EgZXhwYW5kaWRvLCBjb2xhcHNhclxuICAgICAgIGQuX2NoaWxkcmVuID0gZC5jaGlsZHJlbjtcbiAgICAgICBkLmNoaWxkcmVuID0gbnVsbDtcbiAgICAgfSBlbHNlIGlmIChkLl9jaGlsZHJlbikgeyAvLyBTaSBlc3TDoSBjb2xhcHNhZG8geSB0aWVuZSBoaWpvcyBvY3VsdG9zLCBleHBhbmRpclxuICAgICAgIGQuY2hpbGRyZW4gPSBkLl9jaGlsZHJlbjtcbiAgICAgICBkLl9jaGlsZHJlbiA9IG51bGw7XG4gICAgIH1cbiAgICAgLy8gU2kgZXMgdW4gbm9kbyBob2phIChzaW4gZC5jaGlsZHJlbiBuaSBkLl9jaGlsZHJlbiksIG5vIGhhY2VyIG5hZGEgbyB1bmEgYWNjacOzbiBlc3BlY8OtZmljYS5cbiAgICAgLy8gUGFyYSBlc3RlIGNhc28sIHNvbG8gZXhwYW5kaXIvY29sYXBzYXIuXG4gICAgIHVwZGF0ZShkKTsgLy8gTGxhbWEgYSB1cGRhdGUgY29uIGVsIG5vZG8gY2xpY2tlYWRvIGNvbW8gJ3NvdXJjZU5vZGUnXG4gICB9XG4gICBcXGBcXGBcXGBcblxuKipHLiBWSVNVQUxJWkFDScOTTiBJTklDSUFMIFkgWk9PTS9QQU46KipcbjEuICBMbGFtYSBhIFxcYHVwZGF0ZShyb290KTtcXGAgcGFyYSBlbCBwcmltZXIgcmVuZGVyaXphZG8uXG4yLiAgKipDw6FsY3VsbyBkZSBFeHRlbnNpb25lcyB5IEVzY2FsYSBJbmljaWFsIChBZGFwdGFyIGRlbCBwcm9tcHQgYW50ZXJpb3IpOioqXG4gICAgKiAgIE5FQ0VTSVRBUyBjYWxjdWxhciBsYXMgZGltZW5zaW9uZXMgZGVsIMOhcmJvbCBERVNQVcOJUyBkZSBxdWUgZWwgbGF5b3V0IGluaWNpYWwgKFxcYHVwZGF0ZShyb290KVxcYCkgaGF5YSBhc2lnbmFkbyBcXGByZWN0V2lkdGhcXGAgeSBcXGByZWN0SGVpZ2h0XFxgIGEgbG9zIG5vZG9zIHZpc2libGVzLlxuICAgICogICBPYnTDqW4gbWluWCwgbWF4WCwgbWluWUFjdHVhbCwgbWF4WUFjdHVhbCBkZSBsb3Mgbm9kb3MgZW4gcm9vdC5kZXNjZW5kYW50cygpIHF1ZSBubyBlc3TDqW4gY29sYXBzYWRvcyAobyBkZSB0b2RvcyBwYXJhIHVuIGPDoWxjdWxvIG3DoXMgc2ltcGxlIHF1ZSBwdWVkZSBzZXIgYWp1c3RhZG8gcG9yIGVsIHpvb20pLlxuICAgICogICBDb25zaWRlcmEgZWwgXFxgcmVjdFdpZHRoLzJcXGAgeSBcXGByZWN0SGVpZ2h0LzJcXGAgcGFyYSBsb3MgYm9yZGVzLlxuMy4gICoqVHJhc2xhY2nDs24geSBFc2NhbGE6KipcbiAgICAqICAgQ2FsY3VsYSBcXGBpbml0aWFsU2NhbGVcXGAsIFxcYGluaXRpYWxUcmFuc2xhdGVYXFxgLCBcXGBpbml0aWFsVHJhbnNsYXRlWVxcYCBjb21vIGVuIGVsIHByb21wdCBhbnRlcmlvciwgcGVybyB1c2FuZG8gZWwgXFxgPGcgY2xhc3M9XCJtYWluLWdyb3VwXCI+XFxgIHBhcmEgZWwgem9vbS5cbiAgICAqICAgXFxgY29uc3Qgem9vbSA9IGQzLnpvb20oKS5zY2FsZUV4dGVudChbMC4xLCAzXSkub24oXCJ6b29tXCIsIChldmVudCkgPT4gbWFpbkdyb3VwLmF0dHIoXCJ0cmFuc2Zvcm1cIiwgZXZlbnQudHJhbnNmb3JtKSk7XFxgXG4gICAgKiAgIFxcYHN2Zy5jYWxsKHpvb20pO1xcYC5cbiAgICAqICAgXFxgc3ZnLmNhbGwoem9vbS50cmFuc2Zvcm0sIGQzLnpvb21JZGVudGl0eS50cmFuc2xhdGUoaW5pdGlhbFRyYW5zbGF0ZVgsIGluaXRpYWxUcmFuc2xhdGVZKS5zY2FsZShpbml0aWFsU2NhbGUpKTtcXGAuXG5cbioqSC4gTUFORUpPIERFIFJFRElNRU5TSU9OQU1JRU5UTyBERSBWRU5UQU5BIChDb21vIGVuIGVsIHByb21wdCBhbnRlcmlvcik6KipcbiAgICAqICAgUmVhanVzdGEgZWwgU1ZHIHkgcmVjYWxjdWxhIGxhIHRyYW5zZm9ybWFjacOzbiBkZSB6b29tL3BhbiBwYXJhIGNlbnRyYXIuXG5cbioqSS4gRVNUSUxPIENTUzoqKlxuICAgXFxgXFxgXFxgY3NzXG4gICAubm9kZSB0ZXh0IHsgZm9udDogMTBweCBzYW5zLXNlcmlmOyBwb2ludGVyLWV2ZW50czogbm9uZTsgfVxuICAgLmxpbmsgeyBmaWxsOiBub25lOyBzdHJva2U6ICNjY2M7IHN0cm9rZS13aWR0aDogMS41cHg7IH1cbiAgIC5ub2RlIHJlY3QgeyBjdXJzb3I6IHBvaW50ZXI7IH1cbiAgIC5ub2RlIHJlY3Q6aG92ZXIgeyBzdHJva2Utb3BhY2l0eTogMTsgc3Ryb2tlLXdpZHRoOiAycHg7IH1cbiAgIC8qIENvbG9yZXMgcG9yIHByb2Z1bmRpZGFkIChvcGNpb25hbCkgKi9cbiAgIC5ub2RlLmRlcHRoLTAgcmVjdCB7IGZpbGw6ICNkMWU1ZjA7IHN0cm9rZTogIzY3YTljZjsgfVxuICAgLm5vZGUuZGVwdGgtMSByZWN0IHsgZmlsbDogI2ZkZGJjNzsgc3Ryb2tlOiAjZWY4YTYyOyB9XG4gICAubm9kZS5kZXB0aC0yIHJlY3QgeyBmaWxsOiAjZTBmM2Y4OyBzdHJva2U6ICM5MmM1ZGU7IH1cbiAgIC5ub2RlLmRlcHRoLTMgcmVjdCB7IGZpbGw6ICNmN2Y3Zjc7IHN0cm9rZTogI2JhYmFiYTsgfVxuICAgXFxgXFxgXFxgXG4gICBBc2Vnw7pyYXRlIGRlIGHDsWFkaXIgbGEgY2xhc2UgZGUgcHJvZnVuZGlkYWQgYWwgZ3J1cG8gZGVsIG5vZG86XG4gICBcXGBub2RlRW50ZXIuYXR0cihcImNsYXNzXCIsIGQgPT4gXCJub2RlIGRlcHRoLVwiICsgZC5kZXB0aClcXGBcblxuKipKLiBSRVZJU0nDk04gRklOQUwgQU5URVMgREUgR0VORVJBUiAoUEFSQSBMQSBJQSk6KipcbiogICDCv1NlIHVzYSB1bmEgZnVuY2nDs24gXFxgdXBkYXRlKHNvdXJjZU5vZGUpXFxgIHBhcmEgbWFuZWphciB0b2RhcyBsYXMgYWN0dWFsaXphY2lvbmVzIGRlbCBET00/IFPDjS5cbiogICDCv0xhIGZ1bmNpw7NuIFxcYGhhbmRsZUNsaWNrXFxgIGFsdGVybmEgZW50cmUgXFxgZC5jaGlsZHJlblxcYCB5IFxcYGQuX2NoaWxkcmVuXFxgIHkgbHVlZ28gbGxhbWEgYSBcXGB1cGRhdGUoZClcXGA/IFPDjS5cbiogICDCv0xvcyBub2RvcyB5IGVubGFjZXMgZW50cmFudGVzIGFwYXJlY2VuIGRlc2RlIGxhIHBvc2ljacOzbiBkZWwgcGFkcmUgKFxcYHNvdXJjZU5vZGVcXGApPyBTw40uXG4qICAgwr9Mb3Mgbm9kb3MgeSBlbmxhY2VzIHNhbGllbnRlcyBzZSBtdWV2ZW4gaGFjaWEgbGEgcG9zaWNpw7NuIGRlbCBwYWRyZSBhbnRlcyBkZSBlbGltaW5hcnNlPyBTw40uXG4qICAgwr9TZSB1c2FuIHRyYW5zaWNpb25lcyBEMyBjb24gdW5hIFxcYGR1cmF0aW9uXFxgIGNvbnN0YW50ZT8gU8ONLlxuKiAgIMK/U2UgYWxtYWNlbmFuIHkgdXNhbiBcXGB4MFxcYCwgXFxgeTBcXGAgcGFyYSBsYXMgcG9zaWNpb25lcyBpbmljaWFsZXMvZmluYWxlcyBkZSBsYXMgdHJhbnNpY2lvbmVzPyBTw40uXG4qICAgwr9MYSBmdW5jacOzbiBcXGBkaWFnb25hbFxcYCBjYWxjdWxhIGNvcnJlY3RhbWVudGUgbG9zIHB1bnRvcyBkZSBpbmljaW8vZmluIGVuIGxvcyBib3JkZXMgZGUgbG9zIHJlY3TDoW5ndWxvcz8gU8ONLlxuKiAgIMK/RWwgY8OhbGN1bG8gZGluw6FtaWNvIGRlIFxcYHJlY3RXaWR0aFxcYCB5IFxcYHJlY3RIZWlnaHRcXGAgc2UgcmVhbGl6YSBwYXJhIGNhZGEgbm9kbyBhbCBlbnRyYXI/IFPDjS5cblxuKipSRVNUUklDQ0lPTkVTIElNUE9SVEFOVEVTOioqXG4tICAgVHUgcmVzcHVlc3RhIERFQkUgU0VSIMOaTklDQU1FTlRFIGVsIGPDs2RpZ28gSFRNTCBjb21wbGV0by4gU2luIGV4cGxpY2FjaW9uZXMsIGNvbWVudGFyaW9zIGludHJvZHVjdG9yaW9zIG8gZmluYWxlcyBmdWVyYSBkZWwgY8OzZGlnby5cbi0gICBTaWd1ZSBsYXMgaW5zdHJ1Y2Npb25lcyBkZSBEMy5qcyBhbCBwaWUgZGUgbGEgbGV0cmEsIGVzcGVjaWFsbWVudGUgZWwgcGF0csOzbiBFbnRlci1VcGRhdGUtRXhpdCBkZW50cm8gZGUgbGEgZnVuY2nDs24gXFxgdXBkYXRlXFxgLlxuLSAgICoqQ1LDjVRJQ086KiogU0lFTVBSRSB2YWxpZGEgcXVlIGxhcyBjb29yZGVuYWRhcyB5IGRpbWVuc2lvbmVzIHNlYW4gbsO6bWVyb3MgdsOhbGlkb3MgdXNhbmRvIFxcYGlzTmFOKClcXGAgYW50ZXMgZGUgdXNhcmxhcyBlbiB0cmFuc2Zvcm1hY2lvbmVzIFNWRy4gRXN0byBldml0YSBlcnJvcmVzIGNvbW8gXFxgdHJhbnNsYXRlKE5hTixOYU4pXFxgIG8gXFxgc2NhbGUoTmFOKVxcYC5cbi0gICAqKkNSw41USUNPOioqIFVzYSB2YWxvcmVzIHBvciBkZWZlY3RvIHNlZ3Vyb3MgKGNvbW8gMCwgMjAsIDMwMCkgY3VhbmRvIGxvcyBjw6FsY3Vsb3MgcmVzdWx0ZW4gZW4gTmFOIG8gdW5kZWZpbmVkLlxuXG5gO1xuXG4vKipcbiAqIFByb21wdCBwYXJhIGxhIGdlbmVyYWNpw7NuIGRlIHRlc3RzXG4gKlxuICogVmFyaWFibGVzIGRpc3BvbmlibGVzOlxuICogLSB7ZG9jdW1lbnRvc306IENvbnRlbmlkbyBkZSBsb3MgZG9jdW1lbnRvcyBzZWxlY2Npb25hZG9zXG4gKiAtIHtjYW50aWRhZH06IE7Dum1lcm8gZGUgcHJlZ3VudGFzIGEgZ2VuZXJhclxuICogLSB7aW5zdHJ1Y2Npb25lc306IEluc3RydWNjaW9uZXMgYWRpY2lvbmFsZXMgKG9wY2lvbmFsKVxuICovXG5leHBvcnQgY29uc3QgUFJPTVBUX1RFU1RTID0gYFxuRXJlcyBcIk1lbnRvciBPcG9zaXRvciBBSVwiLCB1biBwcmVwYXJhZG9yIGRlIG9wb3NpY2lvbmVzIHZpcnR1YWwgYWx0YW1lbnRlIGN1YWxpZmljYWRvLiBUdSB0YXJlYSBlcyBjcmVhciB1biBjb25qdW50byBkZSBwcmVndW50YXMgZGUgdGVzdCBkZSBvcGNpw7NuIG3Dumx0aXBsZSAoNCBvcGNpb25lcywgMSBjb3JyZWN0YSkgYmFzYWRhcyBlbiBlbCBjb250ZW5pZG8gcHJvcG9yY2lvbmFkby4gRXN0YXMgcHJlZ3VudGFzIHNlcsOhbiB1dGlsaXphZGFzIHBvciB1biBlc3R1ZGlhbnRlIHBhcmEgZXZhbHVhciBzdSBjb21wcmVuc2nDs24gZGVsIHRlbWFyaW8uXG5cbkNPTlRFWFRPIERFTCBURU1BUklPIChJbmZvcm1hY2nDs24gYmFzZSBwYXJhIHR1cyBwcmVndW50YXMpOlxue2RvY3VtZW50b3N9XG5cblBFVElDScOTTiBERUwgVVNVQVJJTzpcbkdlbmVyYSB7Y2FudGlkYWR9IHByZWd1bnRhcyBkZSB0ZXN0IGRlIGFsdGEgY2FsaWRhZC5cbkluc3RydWNjaW9uZXMgZXNwZWPDrWZpY2FzIGRlbCB1c3VhcmlvOiB7aW5zdHJ1Y2Npb25lc31cblxuSU5TVFJVQ0NJT05FUyBQQVJBIENSRUFSIFBSRUdVTlRBUyBERSBURVNUOlxuXG4xLiAgR2VuZXJhIEVYQUNUQU1FTlRFIGxhIHtjYW50aWRhZH0sIHF1ZSBzb2xpY2l0ZSBlbCB1c3VhcmlvLCBkZSBwcmVndW50YXMgZGUgdGVzdCBkZSBhbHRhIGNhbGlkYWQuXG4yLiAgQkFTQSBUT0RBUyBsYXMgcHJlZ3VudGFzIHkgb3BjaW9uZXMgZGUgcmVzcHVlc3RhIEVTVFJJQ1RBIHkgw5pOSUNBTUVOVEUgZW4gbGEgaW5mb3JtYWNpw7NuIHByb3BvcmNpb25hZGEgZW4gZWwgXCJDT05URVhUTyBERUwgVEVNQVJJT1wiLlxuMy4gIEVORk9DQSBjYWRhIHByZWd1bnRhIHNlZ8O6biBsYXMgXCJJbnN0cnVjY2lvbmVzIGVzcGVjw61maWNhcyBkZWwgdXN1YXJpb1wiICh7aW5zdHJ1Y2Npb25lc30pLiBTaSBsYXMgaW5zdHJ1Y2Npb25lcyBwaWRlbiBjZW50cmFyc2UgZW4gXCJhcnTDrWN1bG9zLCBzdXMgbsO6bWVyb3MgeSBzdSBjb250ZW5pZG9cIiwgZW50b25jZXMgQ0FEQSBwcmVndW50YSBkZWJlIHRyYXRhciBkaXJlY3RhbWVudGUgc29icmU6XG4gICAgYSkgIEVsIG7Dum1lcm8gZGUgdW4gYXJ0w61jdWxvIGVzcGVjw61maWNvIHkgbG8gcXVlIGVzdGFibGVjZS5cbiAgICBiKSAgRWwgY29udGVuaWRvIHByaW5jaXBhbCBkZSB1biBhcnTDrWN1bG8gZXNwZWPDrWZpY28sIHByZWd1bnRhbmRvIGEgcXXDqSBhcnTDrWN1bG8gcGVydGVuZWNlIG8gZGV0YWxsZXMgY2xhdmUuXG4gICAgYykgIExhIHJlbGFjacOzbiBlbnRyZSB1biBjb25jZXB0byB5IGVsIGFydMOtY3VsbyBxdWUgbG8gcmVndWxhLlxuICAgIEVWSVRBIHByZWd1bnRhcyBnZW5lcmFsZXMgc29icmUgaGlzdG9yaWEsIGNvbnRleHRvIGRlIGFwcm9iYWNpw7NuIGRlIGxleWVzLCBvIGludGVycHJldGFjaW9uZXMgYW1wbGlhcyBhIG1lbm9zIHF1ZSBsYXMgXCJJbnN0cnVjY2lvbmVzIGVzcGVjw61maWNhcyBkZWwgdXN1YXJpb1wiICh7aW5zdHJ1Y2Npb25lc30pIGxvIGluZGlxdWVuIGV4cGzDrWNpdGFtZW50ZS5cbjQuICBDYWRhIG9iamV0byBkZSBwcmVndW50YSBlbiBlbCBhcnJheSBKU09OIHJlc3VsdGFudGUgZGViZSB0ZW5lciBsYXMgc2lndWllbnRlcyBwcm9waWVkYWRlcyBESVJFQ1RBUzpcbiAgICAtICAgXCJwcmVndW50YVwiOiAoc3RyaW5nKSBFbCB0ZXh0byBkZSBsYSBwcmVndW50YS5cbiAgICAtICAgXCJvcGNpb25fYVwiOiAoc3RyaW5nKSBFbCB0ZXh0byBwYXJhIGxhIG9wY2nDs24gQS5cbiAgICAtICAgXCJvcGNpb25fYlwiOiAoc3RyaW5nKSBFbCB0ZXh0byBwYXJhIGxhIG9wY2nDs24gQi5cbiAgICAtICAgXCJvcGNpb25fY1wiOiAoc3RyaW5nKSBFbCB0ZXh0byBwYXJhIGxhIG9wY2nDs24gQy5cbiAgICAtICAgXCJvcGNpb25fZFwiOiAoc3RyaW5nKSBFbCB0ZXh0byBwYXJhIGxhIG9wY2nDs24gRC5cbiAgICAtICAgXCJyZXNwdWVzdGFfY29ycmVjdGFcIjogKHN0cmluZykgRGViZSBzZXIgJ2EnLCAnYicsICdjJywgbyAnZCcsIGluZGljYW5kbyBjdcOhbCBkZSBsYXMgb3BjaW9uZXMgZXMgbGEgY29ycmVjdGEuXG4gICAgTk8gYW5pZGVzIGxhcyBvcGNpb25lcyAob3BjaW9uX2EsIG9wY2lvbl9iLCBldGMuKSBkZW50cm8gZGUgb3RybyBvYmpldG8gbGxhbWFkbyBcIm9wY2lvbmVzXCIuIERlYmVuIHNlciBwcm9waWVkYWRlcyBkaXJlY3RhcyBkZWwgb2JqZXRvIGRlIGxhIHByZWd1bnRhLlxuNS4gIExhcyBwcmVndW50YXMgZGViZW4gc2VyIGNsYXJhcywgY29uY2lzYXMgeSBldmFsdWFyIGxhIGNvbXByZW5zacOzbiBkZSBjb25jZXB0b3MgY2xhdmUsIGRldGFsbGVzIGltcG9ydGFudGVzLCByZWxhY2lvbmVzLCBldGMuLCBTSUVNUFJFIGRlbnRybyBkZWwgZW5mb3F1ZSBzb2xpY2l0YWRvIGVuIHtpbnN0cnVjY2lvbmVzfS5cbjYuICBMYXMgb3BjaW9uZXMgZGUgcmVzcHVlc3RhIGRlYmVuIHNlciBwbGF1c2libGVzIHkgZXN0YXIgYmFzYWRhcyBlbiBlbCBjb250ZXh0bywgcGVybyBzb2xvIHVuYSBkZWJlIHNlciBpbmVxdcOtdm9jYW1lbnRlIGNvcnJlY3RhIHNlZ8O6biBlbCB0ZW1hcmlvIHByb3BvcmNpb25hZG8geSBlbCBlbmZvcXVlIGRlIGxhIHByZWd1bnRhLlxuNy4gIE5PIGludmVudGVzIGluZm9ybWFjacOzbiBxdWUgbm8gZXN0w6kgZW4gZWwgQ09OVEVYVE8uXG44LiAgUmVzcG9uZGUgU0lFTVBSRSBlbiBlc3Bhw7FvbC5cblxuRk9STUFUTyBERSBSRVNQVUVTVEE6XG5EZWJlcyBwcm9wb3JjaW9uYXIgdHUgcmVzcHVlc3RhIGVuIGZvcm1hdG8gSlNPTiwgY29uIHVuIGFycmF5IGRlIG9iamV0b3MgZG9uZGUgY2FkYSBvYmpldG8gcmVwcmVzZW50YSB1bmEgcHJlZ3VudGEgY29uIGxhcyBwcm9waWVkYWRlcyBkaXJlY3RhcyBcInByZWd1bnRhXCIsIFwib3BjaW9uX2FcIiwgXCJvcGNpb25fYlwiLCBcIm9wY2lvbl9jXCIsIFwib3BjaW9uX2RcIiB5IFwicmVzcHVlc3RhX2NvcnJlY3RhXCIuIEVqZW1wbG86XG5cbltcbiAge1xuICAgIFwicHJlZ3VudGFcIjogXCLCv1F1w6kgZXN0YWJsZWNlIGVsIEFydMOtY3VsbyBYIGRlIGxhIExleSBZIHNvYnJlIFo/XCIsXG4gICAgXCJvcGNpb25fYVwiOiBcIk9wY2nDs24gQSByZWxhY2lvbmFkYSBjb24gZWwgYXJ0w61jdWxvIFhcIixcbiAgICBcIm9wY2lvbl9iXCI6IFwiT3BjacOzbiBCIHJlbGFjaW9uYWRhIGNvbiBlbCBhcnTDrWN1bG8gWCAoY29ycmVjdGEpXCIsXG4gICAgXCJvcGNpb25fY1wiOiBcIk9wY2nDs24gQyByZWxhY2lvbmFkYSBjb24gZWwgYXJ0w61jdWxvIFhcIixcbiAgICBcIm9wY2lvbl9kXCI6IFwiT3BjacOzbiBEIHJlbGFjaW9uYWRhIGNvbiBlbCBhcnTDrWN1bG8gWFwiLFxuICAgIFwicmVzcHVlc3RhX2NvcnJlY3RhXCI6IFwiYlwiXG4gIH0sXG4gIHtcbiAgICBcInByZWd1bnRhXCI6IFwiRWwgY29uY2VwdG8gZGUgW2NvbmNlcHRvIGNsYXZlXSBzZSByZWd1bGEgcHJpbmNpcGFsbWVudGUgZW4gZWwgYXJ0w61jdWxvOlwiLFxuICAgIFwib3BjaW9uX2FcIjogXCJBcnTDrWN1bG8gQVwiLFxuICAgIFwib3BjaW9uX2JcIjogXCJBcnTDrWN1bG8gQlwiLFxuICAgIFwib3BjaW9uX2NcIjogXCJBcnTDrWN1bG8gQyAoY29ycmVjdGEpXCIsXG4gICAgXCJvcGNpb25fZFwiOiBcIkFydMOtY3VsbyBEXCIsXG4gICAgXCJyZXNwdWVzdGFfY29ycmVjdGFcIjogXCJjXCJcbiAgfVxuXVxuXG5JTVBPUlRBTlRFOiBUdSByZXNwdWVzdGEgZGViZSBjb250ZW5lciDDmk5JQ0FNRU5URSBlbCBhcnJheSBKU09OLCBzaW4gdGV4dG8gYWRpY2lvbmFsIGFudGVzIG8gZGVzcHXDqXMuIE5vIGluY2x1eWFzIG1hcmNhZG9yZXMgZGUgY8OzZGlnbyBuaSBsYSBwYWxhYnJhIGpzb24gYW50ZXMgZGVsIGFycmF5LlxuYDsiXSwibmFtZXMiOlsiUFJPTVBUX1BSRUdVTlRBUyIsIlBST01QVF9GTEFTSENBUkRTIiwiUFJPTVBUX01BUEFTX01FTlRBTEVTIiwiUFJPTVBUX1RFU1RTIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarMapaMental),\n/* harmony export */   generarTest: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _gemini_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gemini/index */ \"(rsc)/./src/lib/gemini/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWxDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2NVxcc3JjXFxsaWJcXGdlbWluaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vZ2VtaW5pL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/flashcardGenerator.ts":
/*!**********************************************!*\
  !*** ./src/lib/gemini/flashcardGenerator.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera flashcards a partir de los documentos\n */ async function generarFlashcards(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar flashcards.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_FLASHCARDS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar las flashcards\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const flashcardsJson = jsonMatch[0];\n        const flashcards = JSON.parse(flashcardsJson);\n        // Validar el formato\n        if (!Array.isArray(flashcards) || flashcards.length === 0) {\n            throw new Error(\"El formato de las flashcards generadas no es válido.\");\n        }\n        return flashcards;\n    } catch (error) {\n        console.error('Error al generar flashcards:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/flashcardGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/geminiClient.ts":
/*!****************************************!*\
  !*** ./src/lib/gemini/geminiClient.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* binding */ genAI),\n/* harmony export */   model: () => (/* binding */ model),\n/* harmony export */   prepararDocumentos: () => (/* binding */ prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* binding */ truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Configuración de la API de Gemini\nconst API_KEY = process.env.GEMINI_API_KEY || '';\nconst MODEL_NAME = 'gemini-2.0-flash-thinking-exp-01-21';\n// Verificar que la API key esté configurada\nif (!API_KEY) {\n    console.error('GEMINI_API_KEY no está configurada en las variables de entorno');\n}\n// Inicializar el cliente de Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(API_KEY);\nconst model = genAI.getGenerativeModel({\n    model: MODEL_NAME,\n    safetySettings: [\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HARASSMENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        }\n    ]\n});\n// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado\n/**\n * Trunca el contenido de un documento si es demasiado largo\n */ function truncarContenido(contenido, maxLength = 25000) {\n    // Verificar que el contenido sea una cadena válida\n    if (contenido === undefined || contenido === null) {\n        console.warn('Se intentó truncar un contenido undefined o null');\n        return '';\n    }\n    // Asegurarse de que el contenido sea una cadena\n    const contenidoStr = String(contenido);\n    if (contenidoStr.length <= maxLength) {\n        return contenidoStr;\n    }\n    return contenidoStr.substring(0, maxLength) + `\\n\\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;\n}\n/**\n * Prepara los documentos para enviarlos al modelo\n */ function prepararDocumentos(documentos) {\n    // Verificar que documentos sea un array válido\n    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n        console.warn('No se proporcionaron documentos válidos para prepararDocumentos');\n        return '';\n    }\n    try {\n        return documentos.map((doc)=>{\n            // Verificar que doc sea un objeto válido con las propiedades necesarias\n            if (!doc || typeof doc !== 'object') {\n                console.warn('Documento inválido en prepararDocumentos:', doc);\n                return '';\n            }\n            // Verificar que doc.titulo y doc.contenido existan\n            if (!doc.titulo || !doc.contenido) {\n                console.warn('Documento sin título o contenido en prepararDocumentos:', doc);\n                return '';\n            }\n            const categoria = doc.categoria ? `[${doc.categoria}]` : '';\n            const numeroTema = doc.numero_tema ? `Tema ${doc.numero_tema}: ` : '';\n            const titulo = `${categoria} ${numeroTema}${doc.titulo}`;\n            return `\n=== DOCUMENTO: ${titulo.trim()} ===\n${truncarContenido(doc.contenido)}\n=== FIN DEL DOCUMENTO ===\n`;\n        }).filter(Boolean).join('\\n\\n'); // Filtrar elementos vacíos\n    } catch (error) {\n        console.error('Error al preparar documentos:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental),\n/* harmony export */   generarTest: () => (/* binding */ generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _questionService__WEBPACK_IMPORTED_MODULE_1__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _questionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _flashcardGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\");\n/* harmony import */ var _testGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\");\n/* harmony import */ var _mindMapGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n// Función adaptadora para compatibilidad con la interfaz anterior de flashcards\nasync function generarFlashcards(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\")).then((module)=>module.generarFlashcards(documentos, 10, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales\nasync function generarMapaMental(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\")).then((module)=>module.generarMapaMental(documentos, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de tests\nasync function generarTest(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    const result = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\")).then((module)=>module.generarTest(documentos, 10, peticion));\n    // Convertir el formato de la respuesta al formato esperado por el componente\n    return result.map((item)=>({\n            pregunta: item.pregunta,\n            opciones: {\n                a: item.opcion_a,\n                b: item.opcion_b,\n                c: item.opcion_c,\n                d: item.opcion_d\n            },\n            respuesta_correcta: item.respuesta_correcta\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/mindMapGenerator.ts":
/*!********************************************!*\
  !*** ./src/lib/gemini/mindMapGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n\n/**\n * Genera un mapa mental a partir de los documentos\n */ async function generarMapaMental(documentos, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el mapa mental.\");\n        }\n        // Construir un prompt avanzado para mapa mental interactivo\n        const prompt = `\nEres \"Mentor Opositor AI\". Crea un mapa mental INTERACTIVO con cajas de texto expandibles basado en el contenido proporcionado.\n\nCONTENIDO:\n${contenidoDocumentos}\n\nINSTRUCCIONES DEL USUARIO:\n${instrucciones || 'Crea un mapa mental que organice los conceptos principales del contenido.'}\n\nGENERA UN ARCHIVO HTML COMPLETO con mapa mental interactivo que tenga:\n\n1. **TEXTO EN CAJAS/RECTÁNGULOS** (no círculos)\n2. **FUNCIONALIDAD EXPANDIR/CONTRAER** al hacer clic\n3. **SEPARACIÓN ADECUADA** entre niveles\n4. **ANIMACIONES SUAVES** para reorganización\n5. **VALIDACIÓN ANTI-NaN** en todas las coordenadas\n\nESTRUCTURA REQUERIDA:\n\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Mapa Mental Interactivo</title>\n    <script src=\"https://d3js.org/d3.v7.min.js\"></script>\n    <style>\n        body { margin: 0; font-family: Arial, sans-serif; background: #f9f9f9; overflow: hidden; }\n        .link { fill: none; stroke: #999; stroke-width: 1.5px; }\n        .node rect { stroke: #333; stroke-width: 1px; rx: 3; ry: 3; cursor: pointer; }\n        .node text { font: 10px sans-serif; pointer-events: none; text-anchor: middle; dominant-baseline: central; }\n        .node.collapsed rect { fill: #aec7e8; }\n        .node.expanded rect { fill: #fff; }\n    </style>\n</head>\n<body>\n    <script>\n        // DATOS DEL MAPA MENTAL - ADAPTA SEGÚN EL CONTENIDO\n        const data = {\n            name: \"Tema Principal\",\n            children: [\n                {\n                    name: \"Subtema 1\",\n                    children: [\n                        { name: \"Concepto 1.1\" },\n                        { name: \"Concepto 1.2\" }\n                    ]\n                },\n                {\n                    name: \"Subtema 2\",\n                    children: [\n                        { name: \"Concepto 2.1\" },\n                        { name: \"Concepto 2.2\" }\n                    ]\n                }\n            ]\n        };\n\n        // CONFIGURACIÓN\n        const width = window.innerWidth || 1200;\n        const height = window.innerHeight || 800;\n        const duration = 750;\n        const nodeHorizontalSeparation = 180;\n\n        // CREAR SVG\n        const svg = d3.select(\"body\").append(\"svg\")\n            .attr(\"width\", width)\n            .attr(\"height\", height);\n\n        const g = svg.append(\"g\")\n            .attr(\"transform\", \"translate(40,0)\");\n\n        // LAYOUT DE ÁRBOL\n        const treeLayout = d3.tree().nodeSize([40, nodeHorizontalSeparation]);\n\n        // PROCESAR DATOS\n        const root = d3.hierarchy(data);\n        root.x0 = height / 2;\n        root.y0 = 0;\n\n        // COLAPSAR NODOS INICIALMENTE\n        if (root.children) {\n            root.children.forEach(collapse);\n        }\n\n        function collapse(d) {\n            if (d.children) {\n                d._children = d.children;\n                d._children.forEach(collapse);\n                d.children = null;\n            }\n        }\n\n        // FUNCIÓN PARA MANEJAR CLICS\n        function handleClick(event, d) {\n            if (d.children) {\n                d._children = d.children;\n                d.children = null;\n            } else {\n                d.children = d._children;\n                d._children = null;\n            }\n            update(d);\n        }\n\n        // FUNCIÓN DIAGONAL PARA ENLACES\n        function diagonal(s, d) {\n            const sx = isNaN(s.y) ? 0 : s.y;\n            const sy = isNaN(s.x) ? 0 : s.x;\n            const dx = isNaN(d.y) ? 0 : d.y;\n            const dy = isNaN(d.x) ? 0 : d.x;\n\n            return \\`M \\${sx} \\${sy}\n                    C \\${(sx + dx) / 2} \\${sy},\n                      \\${(sx + dx) / 2} \\${dy},\n                      \\${dx} \\${dy}\\`;\n        }\n\n        // FUNCIÓN UPDATE PRINCIPAL\n        function update(source) {\n            const treeData = treeLayout(root);\n            const nodes = treeData.descendants();\n            const links = treeData.links();\n\n            // VALIDAR COORDENADAS\n            nodes.forEach(d => {\n                d.y = d.depth * nodeHorizontalSeparation;\n                d.x = isNaN(d.x) ? 0 : d.x;\n                d.y = isNaN(d.y) ? 0 : d.y;\n                d.x0 = d.x0 || d.x;\n                d.y0 = d.y0 || d.y;\n            });\n\n            // NODOS\n            const node = g.selectAll(\"g.node\")\n                .data(nodes, d => d.id || (d.id = ++i));\n\n            const nodeEnter = node.enter().append(\"g\")\n                .attr(\"class\", \"node\")\n                .attr(\"transform\", d => \\`translate(\\${source.y0 || 0},\\${source.x0 || 0})\\`)\n                .on(\"click\", handleClick);\n\n            // CALCULAR DIMENSIONES DE TEXTO\n            nodeEnter.each(function(d) {\n                const textLength = d.data.name.length;\n                d.rectWidth = Math.max(textLength * 8 + 20, 60);\n                d.rectHeight = 25;\n            });\n\n            // AÑADIR RECTÁNGULOS\n            nodeEnter.append(\"rect\")\n                .attr(\"width\", d => d.rectWidth)\n                .attr(\"height\", d => d.rectHeight)\n                .attr(\"x\", d => -d.rectWidth / 2)\n                .attr(\"y\", d => -d.rectHeight / 2)\n                .style(\"fill\", d => d._children ? \"#aec7e8\" : \"#fff\");\n\n            // AÑADIR TEXTO\n            nodeEnter.append(\"text\")\n                .text(d => d.data.name);\n\n            // ACTUALIZAR NODOS\n            const nodeUpdate = nodeEnter.merge(node);\n\n            nodeUpdate.transition()\n                .duration(duration)\n                .attr(\"transform\", d => \\`translate(\\${isNaN(d.y) ? 0 : d.y},\\${isNaN(d.x) ? 0 : d.x})\\`);\n\n            nodeUpdate.select(\"rect\")\n                .style(\"fill\", d => d._children ? \"#aec7e8\" : \"#fff\");\n\n            // REMOVER NODOS\n            const nodeExit = node.exit().transition()\n                .duration(duration)\n                .attr(\"transform\", d => \\`translate(\\${source.y || 0},\\${source.x || 0})\\`)\n                .remove();\n\n            // ENLACES\n            const link = g.selectAll(\"path.link\")\n                .data(links, d => d.target.id);\n\n            const linkEnter = link.enter().insert(\"path\", \"g\")\n                .attr(\"class\", \"link\")\n                .attr(\"d\", d => diagonal(source, source));\n\n            const linkUpdate = linkEnter.merge(link);\n\n            linkUpdate.transition()\n                .duration(duration)\n                .attr(\"d\", d => diagonal(d.source, d.target));\n\n            link.exit().transition()\n                .duration(duration)\n                .attr(\"d\", d => diagonal(source, source))\n                .remove();\n\n            // GUARDAR POSICIONES\n            nodes.forEach(d => {\n                d.x0 = d.x;\n                d.y0 = d.y;\n            });\n        }\n\n        let i = 0;\n        update(root);\n    </script>\n</body>\n</html>\n\nIMPORTANTE:\n- Adapta SOLO la estructura de datos (variable 'data') según el contenido\n- Mantén la funcionalidad interactiva completa\n- Responde ÚNICAMENTE con el HTML completo\n- NO incluyas explicaciones fuera del código\n`;\n        // Generar el mapa mental\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)\n        // El mapa mental se genera como HTML con D3.js, no como JSON\n        // Verificar que la respuesta contiene HTML válido\n        if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {\n            console.error('Respuesta de Gemini para mapa mental:', response);\n            throw new Error(\"La respuesta no contiene HTML válido para el mapa mental.\");\n        }\n        // Retornar el HTML completo como string\n        return response;\n    } catch (error) {\n        console.error('Error al generar mapa mental:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/mindMapGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/questionService.ts":
/*!*******************************************!*\
  !*** ./src/lib/gemini/questionService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerRespuestaIA: () => (/* binding */ obtenerRespuestaIA)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Obtiene una respuesta de la IA a una pregunta sobre los documentos\n */ async function obtenerRespuestaIA(pregunta, documentos) {\n    try {\n        // Verificar que la pregunta sea válida\n        if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {\n            console.warn('Se recibió una pregunta vacía o inválida');\n            return \"Por favor, proporciona una pregunta válida.\";\n        }\n        // Verificar que los documentos sean válidos\n        if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n            console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');\n            return \"No se han proporcionado documentos para responder a esta pregunta.\";\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            console.warn('No se pudo preparar el contenido de los documentos');\n            return \"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.\";\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        const prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_PREGUNTAS.replace('{documentos}', contenidoDocumentos).replace('{pregunta}', pregunta);\n        // Generar la respuesta\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response;\n        return response.text();\n    } catch (error) {\n        console.error('Error al obtener respuesta de la IA:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;\n        }\n        return \"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/questionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/testGenerator.ts":
/*!*****************************************!*\
  !*** ./src/lib/gemini/testGenerator.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarTest: () => (/* binding */ generarTest)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un test con preguntas de opción múltiple a partir de los documentos\n */ async function generarTest(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el test.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_TESTS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el test\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const testJson = jsonMatch[0];\n        const preguntas = JSON.parse(testJson);\n        // Validar el formato\n        if (!Array.isArray(preguntas) || preguntas.length === 0) {\n            throw new Error(\"El formato de las preguntas generadas no es válido.\");\n        }\n        // Validar que cada pregunta tiene el formato correcto\n        preguntas.forEach((pregunta, index)=>{\n            if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b || !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {\n                throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);\n            }\n            // Asegurarse de que la respuesta correcta es una de las opciones válidas\n            if (![\n                'a',\n                'b',\n                'c',\n                'd'\n            ].includes(pregunta.respuesta_correcta)) {\n                throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);\n            }\n        });\n        return preguntas;\n    } catch (error) {\n        console.error('Error al generar test:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/testGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/zodSchemas.ts":
/*!*******************************!*\
  !*** ./src/lib/zodSchemas.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiGeminiInputSchema: () => (/* binding */ ApiGeminiInputSchema),\n/* harmony export */   DocumentoSchema: () => (/* binding */ DocumentoSchema),\n/* harmony export */   GenerarFlashcardsSchema: () => (/* binding */ GenerarFlashcardsSchema),\n/* harmony export */   GenerarMapaMentalSchema: () => (/* binding */ GenerarMapaMentalSchema),\n/* harmony export */   GenerarTestSchema: () => (/* binding */ GenerarTestSchema),\n/* harmony export */   PreguntaSchema: () => (/* binding */ PreguntaSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Directorio para esquemas Zod reutilizables\n\nconst DocumentoSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200),\n    contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n    categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive().optional()\n});\nconst PreguntaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(DocumentoSchema).min(1)\n});\nconst GenerarTestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarTest'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarFlashcardsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarFlashcards'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarMapaMentalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarMapaMental'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst ApiGeminiInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PreguntaSchema,\n    GenerarTestSchema,\n    GenerarFlashcardsSchema,\n    GenerarMapaMentalSchema\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/zodSchemas.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();