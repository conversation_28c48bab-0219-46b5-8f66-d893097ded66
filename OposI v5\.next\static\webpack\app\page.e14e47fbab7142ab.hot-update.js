"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TestGenerator.tsx":
/*!******************************************!*\
  !*** ./src/components/TestGenerator.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestGenerator(param) {\n    let { documentosSeleccionados } = param;\n    var _preguntasGeneradas_activeIndex;\n    _s();\n    const [peticion, setPeticion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tituloTest, setTituloTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [descripcionTest, setDescripcionTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [preguntasGeneradas, setPreguntasGeneradas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [testGuardado, setTestGuardado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testsExistentes, setTestsExistentes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [testSeleccionado, setTestSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('nuevo');\n    const [cargandoTests, setCargandoTests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_4__.testFormSchema),\n        defaultValues: {\n            peticion: ''\n        }\n    });\n    // Cargar tests existentes al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestGenerator.useEffect\": ()=>{\n            cargarTests();\n        }\n    }[\"TestGenerator.useEffect\"], []);\n    const cargarTests = async ()=>{\n        setCargandoTests(true);\n        try {\n            const testsData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerTests)();\n            // Obtener el número de preguntas para cada test\n            const testsConContador = [];\n            for (const test of testsData){\n                const numPreguntas = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerPreguntasTestCount)(test.id);\n                testsConContador.push({\n                    ...test,\n                    numPreguntas\n                });\n            }\n            setTestsExistentes(testsConContador);\n        } catch (error) {\n            console.error('Error al cargar tests:', error);\n            setError('No se pudieron cargar los tests existentes.');\n        } finally{\n            setCargandoTests(false);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        setError('');\n        setPreguntasGeneradas([]);\n        setTestGuardado(false);\n        try {\n            const contextos = documentosSeleccionados.map((doc)=>doc.contenido);\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'generarTest',\n                    peticion: data.peticion,\n                    contextos\n                })\n            });\n            if (!response.ok) throw new Error('Error en la API interna');\n            const { result: preguntas } = await response.json();\n            setPreguntasGeneradas(preguntas);\n            if (!tituloTest) {\n                setTituloTest(\"Test: \".concat(data.peticion.substring(0, 50)).concat(data.peticion.length > 50 ? '...' : ''));\n            }\n        } catch (error) {\n            setError('Ha ocurrido un error al generar el test. Por favor, inténtalo de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGuardarTest = async ()=>{\n        if (preguntasGeneradas.length === 0) {\n            setError('No hay preguntas para guardar');\n            return;\n        }\n        // Si se seleccionó crear un nuevo test, validar el título\n        if (testSeleccionado === 'nuevo' && !tituloTest.trim()) {\n            setError('Por favor, proporciona un título para el nuevo test');\n            return;\n        }\n        // Si se seleccionó un test existente, validar que se haya seleccionado uno\n        if (testSeleccionado !== 'nuevo' && testSeleccionado === '') {\n            setError('Por favor, selecciona un test existente');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        try {\n            let testId;\n            // Si es un nuevo test, crearlo\n            if (testSeleccionado === 'nuevo') {\n                testId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearTest)(tituloTest, descripcionTest, documentosSeleccionados.map((doc)=>doc.id));\n                if (!testId) {\n                    throw new Error('No se pudo crear el test');\n                }\n            } else {\n                // Usar el test existente seleccionado\n                testId = testSeleccionado;\n            }\n            // Preparar las preguntas para guardar\n            const preguntasParaGuardar = preguntasGeneradas.map((pg)=>({\n                    test_id: testId,\n                    pregunta: pg.pregunta,\n                    opcion_a: pg.opciones.a,\n                    opcion_b: pg.opciones.b,\n                    opcion_c: pg.opciones.c,\n                    opcion_d: pg.opciones.d,\n                    respuesta_correcta: pg.respuesta_correcta\n                }));\n            // Guardar las preguntas\n            const resultado = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarPreguntasTest)(preguntasParaGuardar);\n            if (!resultado) {\n                throw new Error('No se pudieron guardar las preguntas');\n            }\n            setTestGuardado(true);\n            // Recargar los tests para tener la lista actualizada\n            if (testSeleccionado === 'nuevo') {\n                await cargarTests();\n            }\n        } catch (error) {\n            console.error('Error al guardar las preguntas:', error);\n            setError('Ha ocurrido un error al guardar las preguntas. Por favor, inténtalo de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNextQuestion = ()=>{\n        if (activeIndex < preguntasGeneradas.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    const handlePrevQuestion = ()=>{\n        if (activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    const toggleRespuesta = ()=>{\n        setMostrarRespuesta(!mostrarRespuesta);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-8 border-t pt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold mb-4\",\n                children: \"Generador de Tests\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmitForm(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"peticion\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Describe el test que deseas generar:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"peticion\",\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                rows: 3,\n                                ...register('peticion'),\n                                placeholder: \"Ej: Genera un test sobre los conceptos principales del tema 1\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            errors.peticion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.peticion.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"La IA generar\\xe1 preguntas de test basadas en los documentos seleccionados y tu petici\\xf3n.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                            disabled: isLoading || documentosSeleccionados.length === 0,\n                            children: isLoading ? 'Generando...' : 'Generar Test'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Generando test, por favor espera...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this),\n            preguntasGeneradas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: [\n                            \"Test generado (\",\n                            preguntasGeneradas.length,\n                            \" preguntas)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    !testGuardado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Guardar preguntas de test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tipoTest\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"\\xbfD\\xf3nde quieres guardar estas preguntas?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"nuevoTest\",\n                                                        name: \"tipoTest\",\n                                                        value: \"nuevo\",\n                                                        checked: testSeleccionado === 'nuevo',\n                                                        onChange: ()=>setTestSeleccionado('nuevo'),\n                                                        className: \"mr-2\",\n                                                        disabled: isLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"nuevoTest\",\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: \"Crear nuevo test\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"testExistente\",\n                                                        name: \"tipoTest\",\n                                                        value: \"existente\",\n                                                        checked: testSeleccionado !== 'nuevo',\n                                                        onChange: ()=>{\n                                                            // Seleccionar el primer test por defecto si hay alguno\n                                                            if (testsExistentes.length > 0) {\n                                                                setTestSeleccionado(testsExistentes[0].id);\n                                                            } else {\n                                                                setTestSeleccionado('');\n                                                            }\n                                                        },\n                                                        className: \"mr-2\",\n                                                        disabled: isLoading || testsExistentes.length === 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"testExistente\",\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"A\\xf1adir a un test existente\",\n                                                            testsExistentes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 ml-2\",\n                                                                children: \"(No hay tests disponibles)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this),\n                            testSeleccionado === 'nuevo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tituloTest\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"T\\xedtulo del nuevo test:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"tituloTest\",\n                                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                value: tituloTest,\n                                                onChange: (e)=>setTituloTest(e.target.value),\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"descripcionTest\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Descripci\\xf3n (opcional):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"descripcionTest\",\n                                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                rows: 2,\n                                                value: descripcionTest,\n                                                onChange: (e)=>setDescripcionTest(e.target.value),\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this),\n                            testSeleccionado !== 'nuevo' && testsExistentes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"testExistenteSelect\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Selecciona un test:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"testExistenteSelect\",\n                                        className: \"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        value: testSeleccionado,\n                                        onChange: (e)=>setTestSeleccionado(e.target.value),\n                                        disabled: isLoading,\n                                        children: testsExistentes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: test.id,\n                                                children: [\n                                                    test.titulo,\n                                                    \" \",\n                                                    test.numPreguntas ? \"(\".concat(test.numPreguntas, \" preguntas)\") : ''\n                                                ]\n                                            }, test.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleGuardarTest,\n                                    className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                    disabled: isLoading,\n                                    children: isLoading ? 'Guardando...' : 'Guardar preguntas'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, this),\n                    testGuardado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-100 text-green-800 p-4 rounded-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: testSeleccionado === 'nuevo' ? '¡Nuevo test creado correctamente!' : '¡Preguntas añadidas al test correctamente!'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    \"Puedes acceder a \",\n                                    testSeleccionado === 'nuevo' ? 'él' : 'las preguntas',\n                                    ' desde la secci\\xf3n de \"Mis Tests\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border rounded-lg shadow-md p-6 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrevQuestion,\n                                        disabled: activeIndex === 0,\n                                        className: \"p-2 rounded-full \".concat(activeIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Pregunta \",\n                                            activeIndex + 1,\n                                            \" de \",\n                                            preguntasGeneradas.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextQuestion,\n                                        disabled: activeIndex === preguntasGeneradas.length - 1,\n                                        className: \"p-2 rounded-full \".concat(activeIndex === preguntasGeneradas.length - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-[300px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-lg mb-4\",\n                                            children: (_preguntasGeneradas_activeIndex = preguntasGeneradas[activeIndex]) === null || _preguntasGeneradas_activeIndex === void 0 ? void 0 : _preguntasGeneradas_activeIndex.pregunta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 mt-6\",\n                                            children: [\n                                                'a',\n                                                'b',\n                                                'c',\n                                                'd'\n                                            ].map((opcion)=>{\n                                                var _preguntasGeneradas_activeIndex;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 border rounded-lg \".concat(mostrarRespuesta && preguntasGeneradas[activeIndex].respuesta_correcta === opcion ? 'bg-green-100 border-green-500' : 'hover:bg-gray-50'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 \".concat(mostrarRespuesta && preguntasGeneradas[activeIndex].respuesta_correcta === opcion ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'),\n                                                                children: opcion.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-grow\",\n                                                                children: (_preguntasGeneradas_activeIndex = preguntasGeneradas[activeIndex]) === null || _preguntasGeneradas_activeIndex === void 0 ? void 0 : _preguntasGeneradas_activeIndex.opciones[opcion]\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, opcion, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleRespuesta,\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                    children: mostrarRespuesta ? 'Ocultar respuesta' : 'Mostrar respuesta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Todas las preguntas:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: preguntasGeneradas.map((pregunta, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 \".concat(index === activeIndex ? 'border-indigo-500 bg-indigo-50' : ''),\n                                        onClick: ()=>{\n                                            setActiveIndex(index);\n                                            setMostrarRespuesta(false);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                index + 1,\n                                                \". \",\n                                                pregunta.pregunta\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v5\\\\src\\\\components\\\\TestGenerator.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(TestGenerator, \"SPVVWZMEToIo6pLPN7UkEq4mw+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm\n    ];\n});\n_c = TestGenerator;\nvar _c;\n$RefreshReg$(_c, \"TestGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TestGenerator.tsx\n"));

/***/ })

});